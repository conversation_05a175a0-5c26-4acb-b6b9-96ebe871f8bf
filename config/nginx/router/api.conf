server {
    listen 80;
    server_name party.api.explain.games;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    http2 on;

    server_name party.api.explain.games;

    ssl_certificate /etc/letsencrypt/live/party.explain.games/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/party.explain.games/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    gzip on;
    gzip_types      text/plain application/xml application/json;
    gzip_proxied    no-cache no-store private expired auth;
    gzip_comp_level 6;
    gzip_min_length 1000;

    location / {
        proxy_pass "http://pr-api:8888";
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
    }
}
