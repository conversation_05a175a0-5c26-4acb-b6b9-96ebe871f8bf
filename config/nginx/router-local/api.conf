server {
    listen 80;
    server_name lparty-api.explain.games;

    gzip on;
    gzip_types      text/plain application/xml application/json;
    gzip_proxied    no-cache no-store private expired auth;
    gzip_comp_level 6;
    gzip_min_length 1000;

    location / {
        proxy_pass "http://pr-api:8888";
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
    }
}

server {
    listen 443 ssl;
    http2 on;

    server_name lparty-api.explain.games;

    ssl_certificate     conf.d/certs/tls.crt;
    ssl_certificate_key conf.d/certs/tls.key;
    ssl_protocols       TLSv1 TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers         HIGH:!aNULL:!MD5;

    gzip on;
    gzip_types      text/plain application/xml application/json;
    gzip_proxied    no-cache no-store private expired auth;
    gzip_min_length 1000;

    location / {
        proxy_pass "http://pr-api:8888";
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
    }
}

