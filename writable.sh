#!/bin/bash

# Script to make necessary directories writable for party.explain.games project
# Run this after cloning or deploying the project

echo "Setting up writable directories for party.explain.games project..."

make_dir_writable() {
    local dir_path="$1"
    if [ -d "$dir_path" ]; then
        chmod 777 "$dir_path/" -R
        echo "✓ Made $dir_path/ writable (777)"
    else
        echo "⚠ Directory $dir_path/ not found"
    fi
}

make_dir_writable "packages/api/src/temp"
make_dir_writable "packages/api/src/temp_community"
make_dir_writable "packages/api/src/temp_event"

# You can add other directories that need to be writable here
# Example:
# if [ -d "data/static" ]; then
#     chmod 755 data/static/
#     echo "✓ Made data/static/ writable (755)"
# fi

echo "Directory permissions setup complete!"