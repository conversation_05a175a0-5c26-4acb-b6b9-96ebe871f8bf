import { resolve } from "path"

import react from "@vitejs/plugin-react"
import autoprefixer from "autoprefixer"
import dotenv from "dotenv"
import postcssModulesValues from "postcss-modules-values"
import { defineConfig, loadEnv } from "vite"
import svgr from "vite-plugin-svgr"

export default defineConfig(({ mode }) => {
  const isDev = mode === "development"

  // Load env file based on mode
  const env = loadEnv(mode, process.cwd(), "")

  // Load .env file for non-dev environments
  if (!isDev) {
    dotenv.config({ path: "./.env" })
  }

  return {
    plugins: [
      react(),
      svgr({
        include: "**/*.svg",
      }),
    ],

    define: {
      ENV_API_URL: JSON.stringify(process.env.API_SERVER_URL),
      ENV_IMAGE_CDN: JSON.stringify(process.env.CDN_SERVER_URL),
      ENV_MODE: JSON.stringify(isDev ? "dev" : "prod"),
      ENV_COMMUNITY_DEFAULT_LOGO: JSON.stringify(
        process.env.COMMUNITY_DEFAULT_LOGO,
      ),
    },

    css: {
      modules: {
        localsConvention: "camelCase",
        generateScopedName: isDev
          ? "[name]__[local]___[hash:base64:5]"
          : "[hash:base64]",
      },
      postcss: {
        plugins: [postcssModulesValues, autoprefixer],
      },
      devSourcemap: isDev,
    },

    resolve: {
      extensions: [".tsx", ".ts", ".js", ".mjs", ".css", ".svg"],
      alias: {
        "@": resolve(__dirname, "src"),
      },
    },

    build: {
      outDir: isDev
        ? "build"
        : resolve(__dirname, "../../express/src/src/static"),
      emptyOutDir: true,
      sourcemap: isDev,
      rollupOptions: {
        output: {
          manualChunks: isDev
            ? undefined
            : {
                vendor: ["react", "react-dom"],
                mui: ["@mui/material", "@mui/icons-material"],
                router: ["@tanstack/react-router"],
                query: ["@tanstack/react-query"],
              },
          chunkFileNames: "[name].[hash].js",
          entryFileNames: "[name].[hash].js",
          assetFileNames: "[name].[hash].[ext]",
        },
      },
      minify: !isDev,
    },

    server: {
      allowedHosts: [
        "explain.games",
        "party.explain.games",
        "lparty.explain.games",
      ],
      port: 443,
      https: false, // You may need to configure SSL certificates
      host: true,
      open: false,
      hmr: {
        overlay: {
          errors: true,
          warnings: false,
        },
      },
    },

    preview: {
      port: 443,
      https: true,
    },
  }
})
