# Webpack to Vite Migration Guide

## Files Created/Modified

### 1. New Files Created:
- `vite.config.ts` - Main Vite configuration
- `index.html` - HTML template (replaces index.ejs)

### 2. Modified Files:
- `package.json` - Updated scripts to use Vite commands

## Dependencies to Install

Run the following command to install Vite dependencies:

```bash
pnpm add -D vite @vitejs/plugin-react @types/node vite-plugin-svgr
```

## Dependencies to Remove

You can remove these webpack-specific dependencies:

```bash
pnpm remove webpack webpack-cli webpack-dev-server html-webpack-plugin mini-css-extract-plugin css-loader style-loader postcss-loader ts-loader css-minimizer-webpack-plugin @svgr/webpack
```

## Key Changes Made

### 1. Build Tool Migration
- **Webpack** → **Vite**
- Faster development server with HMR
- Better tree-shaking and bundling

### 2. Configuration Mapping

| Webpack Feature | Vite Equivalent |
|----------------|-----------------|
| `entry: "./src/index.tsx"` | Automatically detected from `index.html` |
| `HtmlWebpackPlugin` | Built-in HTML processing |
| `MiniCssExtractPlugin` | Built-in CSS extraction |
| `webpack.DefinePlugin` | `define` option |
| `@svgr/webpack` | `vite-plugin-svgr` |
| CSS Modules | Built-in support |
| PostCSS | Built-in support |

### 3. Environment Variables
- Webpack: `ENV_API_URL` (custom define)
- Vite: Same custom defines maintained for compatibility

### 4. Development Server
- Port: 443 (maintained)
- HTTPS: Enabled (you may need to configure SSL certificates)
- HMR: Enabled with error overlay

### 5. Build Output
- Development: `build/` directory
- Production: `../../express/src/src/static/` (maintained)
- File naming: `[name].[hash].[ext]` format maintained

## Scripts Updated

| Old Script | New Script |
|-----------|------------|
| `npm run build` | Uses `vite build` |
| `npm run start` | Uses `vite` |
| `npm run start:local` | Uses `vite --config vite.local.config.ts` |

## Additional Notes

### 1. SSL Certificates
The development server is configured for HTTPS on port 443. You may need to:
- Configure SSL certificates in the Vite config
- Or modify the server config to use HTTP for local development

### 2. Local Development Config
You may want to create a `vite.local.config.ts` for local development with different settings.

### 3. CSS Modules
CSS modules are configured to work the same way as your webpack setup:
- Development: `[name]__[local]___[hash:base64:5]`
- Production: `[hash:base64]`

### 4. Code Splitting
Vite automatically handles code splitting, but manual chunks are configured for:
- `vendor`: React and React DOM
- `mui`: Material-UI components
- `router`: Tanstack Router
- `query`: Tanstack Query

### 5. TypeScript
Vite has built-in TypeScript support, no need for ts-loader.

## Testing the Migration

1. Install the new dependencies
2. Remove the old webpack dependencies
3. Run `npm run start` to test development server
4. Run `npm run build` to test production build
5. Verify all features work as expected

## Rollback Plan

If you need to rollback:
1. Keep the original `webpack.config.js`
2. Revert the `package.json` scripts
3. Remove the Vite config files
4. Reinstall webpack dependencies
