import { Alert, Box, Grid2, Typography } from "@mui/material"

import { LoginButton } from "../../../components/common/LoginButton/LoginButton"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { hasPermission } from "../../../permissions"
import {
  COMMUNITY_OPEN_ROUTE,
  CREATE_COMMUNITY_ROUTE,
  PROFILE_ROUTE,
  PUBLIC_COMMUNITIES_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"

export const IndexPage = () => {
  const { isLoggedIn, userData } = useUserStore()

  return (
    <Box
      alignContent="center"
      justifyContent="center"
      display="flex"
      flexDirection="column"
      padding={5}
    >
      <Box
        alignContent="center"
        justifyContent="center"
        display="flex"
        flexDirection="column"
        gap={2}
      >
        <Typography variant="h5">Welcome to the Party App!</Typography>
        <Typography variant="body1">
          This app is meant to create groups for boradgamers to share their
          games with friends and/or local communities.
        </Typography>
        {!isLoggedIn && (
          <Alert severity="warning" title="Login">
            To start exploring please <LoginButton /> first!
          </Alert>
        )}
        {isLoggedIn && (
          <Grid2 container columns={12}>
            <Grid2 size={12}>
              <Typography variant="body1">
                View all public communities:{" "}
                <PartyLink to={PUBLIC_COMMUNITIES_ROUTE} variant="text">
                  Public communities
                </PartyLink>
              </Typography>
            </Grid2>
            <Grid2 size={12}>
              <Typography variant="body1">
                View all communities that I have joined or created:{" "}
                <PartyLink to={COMMUNITY_OPEN_ROUTE} variant="text">
                  My communities
                </PartyLink>
              </Typography>
            </Grid2>
            <Grid2 size={12}>
              <Typography variant="body1">
                Edit my general information:{" "}
                <PartyLink to={PROFILE_ROUTE} variant="text">
                  Profile
                </PartyLink>
              </Typography>
            </Grid2>
            {hasPermission(userData, "global", "createCommunity", {
              createdCommunities: userData.countCommunities,
            }) ? (
              <Grid2 size={12}>
                <Typography variant="body1" fontWeight="bold">
                  Create new community:{" "}
                  <PartyLink to={CREATE_COMMUNITY_ROUTE} variant="text">
                    Create community
                  </PartyLink>
                </Typography>
              </Grid2>
            ) : (
              <Grid2 size={12}>
                <Typography variant="body1">
                  To create Your own community You need to gain permission from
                  site administration for now.
                </Typography>
              </Grid2>
            )}
          </Grid2>
        )}
      </Box>
    </Box>
  )
}
