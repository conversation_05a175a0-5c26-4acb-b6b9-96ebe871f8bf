import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { UserList } from "../../../components/user/UserList/UserList"
import { eventParticipantsRoute } from "../../../routes/event/eventParticipants.route"
import { EVENT_ROOT_ROUTE } from "../../../routes/paths"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

export const EventParticipantsPage = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const participants = eventParticipantsRoute.useLoaderData()

  if (!event || !isEvent(event) || !participants) {
    return null
  }

  return (
    <>
      <TitleRow title="Participants" />
      <UserList
        users={participants}
        eventId={event.id}
        labelInfo="Participant: "
      />
    </>
  )
}
