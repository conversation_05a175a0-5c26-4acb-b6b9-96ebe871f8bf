import { Box } from "@mui/material"
import { useRouter } from "@tanstack/react-router"
import React, { useEffect } from "react"

import { EventConfiguration } from "../../../components/events/EventConfiguration/EventConfiguration"
import { EventUploadImage } from "../../../components/events/EventUploadImage/EventUploadImage"
import { hasPermission } from "../../../permissions"
import { EVENT_ROOT_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

import * as styles from "./editEventPage.module.css"

export const EditEventPage = () => {
  const [saved, setSaved] = React.useState(false)
  const router = useRouter()
  const [savedImage, setSavedImage] = React.useState<number>(0)
  const base = useParentRouteData(EVENT_ROOT_ROUTE)

  useEffect(() => {
    if (savedImage > 0) {
      router.invalidate()
    }
  }, [savedImage, router])

  const user = useUserStore((state) => state.userData)

  if (!isEvent(base)) {
    return null
  }

  if (!hasPermission(user, "event", "update", { id: base.id })) {
    return null
  }

  return (
    <Box className={styles.container}>
      <EventUploadImage
        id={base.id}
        onSuccess={() => setSavedImage(savedImage + 1)}
        hasImage={base.image}
        notNew={true}
      />
      <EventConfiguration edit={base} onSuccess={() => setSaved(true)} />
    </Box>
  )
}
