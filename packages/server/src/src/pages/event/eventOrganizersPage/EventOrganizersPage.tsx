import { Box, Button, ButtonGroup, Typography } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback } from "react"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import {
  CommunityThumbnail,
  ICCommunity,
} from "../../../components/community/CommunityThumbnail/CommunityThumbnail"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { UserList } from "../../../components/user/UserList/UserList"
import { eventOrganizersRoute } from "../../../routes/event/eventOrganizers.route"
import {
  COMMUNITY_ROUTE,
  EVENT_MANAGE_ROUTE,
  EVENT_ROOT_ROUTE,
} from "../../../routes/paths"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

export const EventOrganizersPage = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const data = eventOrganizersRoute.useLoaderData()

  const navigate = useNavigate()

  const onCommunityClick = useCallback(
    (id: number) => {
      navigate({ to: COMMUNITY_ROUTE, params: { communityId: id.toString() } })
    },
    [navigate],
  )

  if (!data || !event || !isEvent(event)) {
    return null
  }

  const { communities, hosts } = data

  return (
    <Box>
      <TitleRow title="Organizers">
        <Button variant="outlined">Invite Community</Button>
      </TitleRow>
      <Box>
        <Typography variant="h6">Communities</Typography>
        <Box
          alignContent="center"
          justifyContent="center"
          display="flex"
          flexDirection="row"
          gap={4}
          flexWrap="wrap"
          padding={5}
        >
          {communities.map((community) => (
            <CommunityThumbnail
              community={community as ICCommunity}
              onClick={() => onCommunityClick(community.id)}
            />
          ))}
        </Box>
      </Box>
      {hosts && (
        <Box>
          <Typography variant="h6">Hosts</Typography>
          <Box>
            <UserList users={hosts} eventId={event.id} labelInfo="Host: " />
          </Box>
        </Box>
      )}
    </Box>
  )
}
