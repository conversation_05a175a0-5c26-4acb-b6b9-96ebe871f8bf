import { Box, Tab, Tabs } from "@mui/material"
import React from "react"

import * as styles from "./mobileTabSelect.module.css"

interface MobileTabSelectProps {
  tab?: string
  defaultTab: string
  onChange: (newTab: string) => void
  isWelcome: boolean
}
export const MobileTabSelect = ({
  tab,
  defaultTab,
  onChange,
  isWelcome,
}: MobileTabSelectProps) => {
  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    onChange(newValue)
  }

  return (
    <Box className={styles.tabWrapper}>
      <Tabs
        scrollButtons
        variant="scrollable"
        allowScrollButtonsMobile
        value={tab ?? defaultTab}
        onChange={handleChange}
      >
        <Tab value="info" label="Info" />
        {isWelcome && <Tab value="welcome" label="Welcome" />}
        <Tab value="games" label="Games" />
        <Tab value="participants" label="Participants" />
      </Tabs>
    </Box>
  )
}
