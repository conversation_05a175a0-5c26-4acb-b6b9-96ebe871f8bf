import { Box } from "@mui/material"

import { EVENT_ROOT_ROUTE } from "../../../../routes/paths"
import { isEvent, useParentRouteData } from "../../../../utils/pages.rootObject"

export const Welcome = () => {
  const base = useParentRouteData(EVENT_ROOT_ROUTE)

  if (!isEvent(base)) {
    return null
  }

  return (
    <Box width="100%" pt={2} mb={2} borderBottom="1px solid #ccc">
      <Box
        width="100%"
        p={4}
        pb={8}
        boxSizing="border-box"
        maxWidth="1000px"
        margin="auto"
        lineHeight="2em"
        textAlign="center"
      >
        <div dangerouslySetInnerHTML={{ __html: base?.description ?? "" }} />
      </Box>
    </Box>
  )
}
