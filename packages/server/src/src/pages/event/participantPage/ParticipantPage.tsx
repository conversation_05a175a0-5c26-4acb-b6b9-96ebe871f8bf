import { Box } from "@mui/material"

import { hasPermission } from "../../../permissions"
import { eventParticipantRoute } from "../../../routes/event/eventParticipant.route"
import { EVENT_ROOT_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

import { MenuBar } from "./components/MenuBar"
import { UserInfo } from "./components/UserInfo"
import * as styles from "./participantPage.module.css"

export const ParticipantPage = () => {
  const base = useParentRouteData(EVENT_ROOT_ROUTE)
  const user = eventParticipantRoute.useLoaderData()
  const myData = useUserStore((store) => store.userData)
  const search = eventParticipantRoute.useSearch()
  if (!isEvent(base) || !user) return null

  const canApprove = hasPermission(myData, "event", "approve", {
    id: base.id,
  })

  const canShare = hasPermission(myData, "event", "shareUserGames", {
    id: base.id,
    viewedUserId: user.id,
    allowShare: base.share,
  })

  return (
    <Box className={styles.container}>
      <MenuBar participantId={user.id} eventId={base?.id ?? 0} />
      {(!search.tab || search.tab === "profile") && (
        <Box padding={4}>
          <UserInfo
            canApprove={canApprove}
            user={user}
            canShare={canShare}
            eventId={base?.id ?? 0}
          />
        </Box>
      )}
    </Box>
  )
}
