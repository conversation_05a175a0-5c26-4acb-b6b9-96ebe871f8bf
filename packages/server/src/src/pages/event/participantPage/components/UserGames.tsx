import { useNavigate } from "@tanstack/react-router"
import { useCallback, useMemo } from "react"

import { type ICTag } from "../../../../components/games/GameSearch/components/ChipList"
import { type ICSearchBoxTag } from "../../../../components/games/GameSearch/components/SearchBox"
import { type ICUserThumbnailWrapperGame } from "../../../../components/games/GamesThumbnailView/GameThumbnailWrapper"
import { GamesThumbnailView } from "../../../../components/games/GamesThumbnailView/GamesThumbnailView"
import { memberRoute } from "../../../../routes/community/member.route"
import { COMMUNITY_USER_ROUTE, GAME_ROUTE } from "../../../../routes/paths"
import { useIsMobileStore } from "../../../../store/useIsMobileStore"
import { applyFilters } from "../../../../utils/filter"

interface UserExpansionsProps {
  games: (ICUserThumbnailWrapperGame & {
    weight: number
    players: {
      box: { min: number; max: number }
      stats?: number[][] | undefined
    }
    tags: number[]
  })[]
  communityId: number
  tags?: ICSearchBoxTag[]
}

export const UserGames = ({
  games,
  communityId,
  tags,
}: UserExpansionsProps) => {
  const params = memberRoute.useParams()
  const search = memberRoute.useSearch()
  const navigate = useNavigate()
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  const onChange = useCallback(
    (page: number) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params,
        search: {
          ...search,
          page,
          tab: "games",
        },
      }),
    [navigate, params, search],
  )

  const optimizeTags = useMemo(() => {
    const populatedTags: ICTag[][] = []

    games.forEach((currentGame) => {
      populatedTags[currentGame.id] = currentGame.tags!.map(
        (currentTag) => (tags ?? []).find((tag) => tag.id === currentTag)!,
      )
    })

    return populatedTags
  }, [tags])

  const useGameList = useMemo(() => {
    return applyFilters(games, search, optimizeTags)
  }, [games, search])

  const isLargeTablet = sizeThresholdList.largeTablet

  return (
    <GamesThumbnailView
      listMode={isLargeTablet}
      navigation={{
        to: GAME_ROUTE,
        params: {
          communityId: String(params?.communityId ?? 0),
        },
        search: {
          sourcePage: "user",
          sourceProps: JSON.stringify(search),
          sourceParams: JSON.stringify({
            userId: String(params.userId),
          }),
        },
      }}
      games={useGameList}
      onPageChange={onChange}
      communityId={communityId}
      page={search.page ?? 1}
    />
  )
}
