import { CommunityList } from "../../../components/community/CommunityList/CommunityList"
import { publicCommunitiesRoute } from "../../../routes/index/publicCommunities.route"
import { type RouterOutput } from "../../../trpc/trpc"

type CommunityListData = RouterOutput["communityList"]

export const PublicCommunitiesPage = () => {
  const communities: CommunityListData | null =
    publicCommunitiesRoute.useLoaderData()

  if (!communities) {
    return <></>
  }

  //return null
  return <CommunityList title="Public Communities" communities={communities} />
}
