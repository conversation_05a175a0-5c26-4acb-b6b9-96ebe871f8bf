.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100vh;
  min-height: 450px;
  box-sizing: border-box;
  gap: var(--spacing-2);
  margin-top: var(--spacing-6);
}

.community {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-1);
}

.buttons {
  display: flex;
  flex-direction: row;
  gap: var(--spacing-1);
}
