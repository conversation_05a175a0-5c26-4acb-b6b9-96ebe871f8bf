import { GameListItem } from "../../../../components/games/GameListItem/GameListItem"
import { GameThumbnail } from "../../../../components/games/GameThumbnail/GameThumbnail"
import { useIsMobileStore } from "../../../../store/useIsMobileStore"

import * as styles from "./expansionThumbnail.module.css"

import type { IUserProfileGameExpansion } from "../../../../types/tRPC.types"

interface ExpansionThumbnailProps {
  expansion: IUserProfileGameExpansion
  onOpenItem: (id: number) => void
}
export const GameThumbnailWrapper = ({
  expansion,
  onOpenItem,
}: ExpansionThumbnailProps) => {
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  const isLargeTablet = sizeThresholdList.largeTablet

  const Component = isLargeTablet ? GameListItem : GameThumbnail

  return (
    <Component
      className={styles.card}
      isBase={expansion.type === "base" || expansion.type === "base-expansion"}
      onClick={onOpenItem}
      isExpansion={
        expansion.type !== "base" && expansion.type !== "base-expansion"
      }
      game={expansion}
    />
  )
}
