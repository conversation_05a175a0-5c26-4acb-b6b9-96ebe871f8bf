import { Box, FormControl, TextField, Typography } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import debounce from "debounce"
import {
  type ChangeEvent,
  type KeyboardEvent,
  useCallback,
  useMemo,
  useState,
} from "react"

import {
  ITEM_INFO_MODAL_NAME,
  ItemInfoModal,
} from "../../../../components/modals/ItemInfoModal/ItemInfoModal"
import { usersProfileGameRoute } from "../../../../routes/index/usersProfileGame.route"
import { PROFILE_GAME_ROUTE } from "../../../../routes/paths"
import { useIsMobileStore } from "../../../../store/useIsMobileStore"
import { useModalStore } from "../../../../store/useModalStore"

import { GameThumbnailWrapper } from "./GameThumbnailWrapper"

import type { IUserProfileGameExpansion } from "../../../../types/tRPC.types"

interface ExpansionListProps {
  expansions: IUserProfileGameExpansion[]
}

const sort = (s1: IUserProfileGameExpansion, s2: IUserProfileGameExpansion) => {
  if (
    (s1.type === "base" || s1.type === "base-expansion") &&
    s2.type !== "base" &&
    s2.type !== "base-expansion"
  ) {
    return -1
  }

  if (
    (s2.type === "base" || s2.type === "base-expansion") &&
    s1.type !== "base" &&
    s1.type !== "base-expansion"
  ) {
    return 1
  }
  return s1.bggId > s2.bggId ? 1 : -1
}

export const ExpansionList = ({ expansions }: ExpansionListProps) => {
  const { search } = usersProfileGameRoute.useSearch()
  const navigate = useNavigate()
  const [openItem, setOpenItem] = useState<number | null>(null)
  const linkParams = usersProfileGameRoute.useParams()
  const [searchText, setSearchText] = useState(search)
  const { openModal } = useModalStore()
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  const isLargeTablet = sizeThresholdList.largeTablet

  const onSearch = useCallback(
    (search: string) => {
      navigate({
        to: PROFILE_GAME_ROUTE,
        params: {
          gameId: linkParams.gameId,
        },
        search: {
          search: search,
        },
      })
    },
    [linkParams],
  )

  const onOpenItem = useCallback(
    (id: number) => {
      setOpenItem(id)
      openModal(ITEM_INFO_MODAL_NAME)
    },
    [setOpenItem, openModal],
  )

  const onSearchDelayed = useMemo(() => debounce(onSearch, 100), [onSearch])

  const onSearchType = (event: ChangeEvent<HTMLInputElement>) => {
    onSearchDelayed(event.target.value)
    setSearchText(event.target.value)
  }

  const onKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Escape") {
      setSearchText("")
      onSearch("")
    }
  }

  const searchLower = search?.toLowerCase() ?? ""

  return (
    <Box>
      <Box mb={2} display="flex" justifyContent="space-between">
        <Typography variant="h6">Owned game items</Typography>
        <FormControl>
          <TextField
            label="Search"
            variant="outlined"
            value={searchText}
            onChange={onSearchType}
            onKeyDown={onKeyDown}
          />
        </FormControl>
      </Box>
      <Box
        display="flex"
        gap={isLargeTablet ? 0 : 2}
        flexWrap="wrap"
        justifyContent="center"
      >
        {expansions
          .sort(sort)
          .filter((expansion) =>
            expansion.title.toLowerCase().includes(searchLower),
          )
          .map((expansion: IUserProfileGameExpansion) => (
            <GameThumbnailWrapper
              key={expansion.id}
              expansion={expansion}
              onOpenItem={onOpenItem}
            />
          ))}
      </Box>
      <ItemInfoModal itemId={openItem} />
    </Box>
  )
}
