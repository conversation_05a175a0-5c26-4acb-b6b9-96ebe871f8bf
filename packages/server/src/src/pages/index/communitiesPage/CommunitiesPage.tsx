import { CommunityList } from "../../../components/community/CommunityList/CommunityList"
import { communityListRoute } from "../../../routes/community/community.route"
import { type RouterOutput } from "../../../trpc/trpc"

type CommunityListData = RouterOutput["communityList"]

export const CommunitiesPage = () => {
  const communities: CommunityListData | null =
    communityListRoute.useLoaderData()

  if (!communities) {
    return <></>
  }

  return <CommunityList title="My Communities" communities={communities} />
}
