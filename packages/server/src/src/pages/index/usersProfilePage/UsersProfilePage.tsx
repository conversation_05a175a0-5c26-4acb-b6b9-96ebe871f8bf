import { usersProfileRoute } from "../../../routes/index/usersProfile.route"

import { MenuBar } from "./components/MenuBar"
import { GamesSubpage } from "./subpages/GamesSubpage"
import { ProfileSubpage } from "./subpages/ProfileSubpage"

export const UsersProfilePage = () => {
  const search = usersProfileRoute.useSearch()

  return (
    <div>
      <MenuBar />
      {(!search.tab || search.tab === "profile") && <ProfileSubpage />}
      {search.tab === "games" && <GamesSubpage />}
    </div>
  )
}
