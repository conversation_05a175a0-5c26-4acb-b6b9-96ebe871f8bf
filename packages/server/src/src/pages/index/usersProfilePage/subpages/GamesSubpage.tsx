import { usersProfileRoute } from "../../../../routes/index/usersProfile.route"
import { UserGames } from "../components/UserGames"

export const GamesSubpage = () => {
  const userData = usersProfileRoute.useLoaderData()
  if (!userData) return null

  return (
    userData.games && (
      <UserGames
        games={userData.games}
        tagCategories={userData.tagCategories}
        tags={userData.tags}
      />
    )
  )
}
