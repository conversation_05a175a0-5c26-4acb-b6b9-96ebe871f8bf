import { Box } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import React, { useEffect, useState } from "react"

import { EventConfiguration } from "../../../components/events/EventConfiguration/EventConfiguration"
import { EventUploadImage } from "../../../components/events/EventUploadImage/EventUploadImage"
import { hasPermission } from "../../../permissions"
import { EVENT_ROUTE_INFO } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"

import * as styles from "./createEventPage.module.css"

export const CreateEventPage = () => {
  const [uploaded, setUploaded] = React.useState(false)
  const [created, setCreated] = useState<null | number>(null)
  const user = useUserStore((state) => state.userData)
  const navigate = useNavigate()

  if (!hasPermission(user, "global", "createEvent")) {
    return null
  }

  useEffect(() => {
    if (created !== null && uploaded) {
      navigate({
        to: EVENT_ROUTE_INFO,
        params: {
          eventId: String(created),
        },
      })
    }
  }, [created, uploaded])

  return (
    <Box className={styles.container}>
      {!created && <EventConfiguration onSuccess={setCreated} />}
      {created && !uploaded && (
        <EventUploadImage onSuccess={() => setUploaded(true)} id={created} />
      )}
    </Box>
  )
}
