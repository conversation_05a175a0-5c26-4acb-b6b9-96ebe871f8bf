import { Box } from "@mui/material"

import { tagListRoute } from "../../../routes/index/tagList.route"

import { ChipList } from "./components/ChipList"
import * as styles from "./tagListPage.module.css"

export const TagListPage = () => {
  const response = tagListRoute.useLoaderData()

  if (!response) return null

  const { tags, tagCategories } = response

  return (
    <Box className={styles.container}>
      <ChipList tagList={tags} tagCategories={tagCategories} />
    </Box>
  )
}
