import { Box, Chip, Typography } from "@mui/material"

interface Tag {
  title: string
  type: number | null
}

interface ChipListProps {
  tagList: Tag[]
  tagCategories: {
    id: number
    title: string
    color: string
  }[]
}

const sortTags = (t1: Tag, t2: Tag) => {
  if ((t1.type ?? 0) > (t2.type ?? 0)) {
    return 1
  }

  if ((t1.type ?? 0) < (t2.type ?? 0)) {
    return -1
  }

  return t1.title < t2.title ? -1 : 1
}
export const ChipList = ({ tagList, tagCategories }: ChipListProps) => {
  const getCat = (type: number | null) => {
    return tagCategories.find((cat) => cat.id === type)
  }

  const tagListNew = tagList.reduce(
    (groups: Record<string | number, Tag[]>, tag) => {
      const key = tag.type ?? ""
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(tag)
      return groups
    },
    {},
  )

  return (
    <Box
      padding={2}
      gap={2}
      display="flex"
      flexDirection="column"
      flexWrap="wrap"
    >
      <Typography variant="body1">
        Searchable tag list. Just type in search single or in any combination.
        This list is not clickable, but just informative!
      </Typography>
      {Object.entries(tagListNew).map(([key, list]) => {
        return (
          <Box>
            <Typography variant="h6" key={key}>
              {getCat(parseInt(key))?.title.toLocaleUpperCase()}
            </Typography>
            <Box gap={0.5} display="flex" flexDirection="row" flexWrap="wrap">
              {list?.sort(sortTags).map((tag) => (
                <Chip
                  sx={{
                    backgroundColor: `#${getCat(tag.type)?.color}`,
                  }}
                  title={`${tag.type}: ${tag.title}`}
                  key={tag.title}
                  component="div"
                  label={tag.title}
                  variant="outlined"
                />
              ))}
            </Box>
          </Box>
        )
      })}
    </Box>
  )
}
