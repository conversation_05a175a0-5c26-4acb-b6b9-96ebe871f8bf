import { Box } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback } from "react"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { EventFilter } from "../../../components/events/EventFilter/EventFilter"
import { EventSearch } from "../../../components/events/EventSearch/EventSearch"
import { EventsList } from "../../../components/events/EventsList/EventsList"
import { hasPermission } from "../../../permissions"
import { eventsRoute } from "../../../routes/index/events.route"
import { CREATE_EVENT_ROUTE, EVENTS_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"

export const EventsPage = () => {
  const events = eventsRoute.useLoaderData()
  const search = eventsRoute.useSearch()
  const navigate = useNavigate({ from: EVENTS_ROUTE })
  const userInfo = useUserStore((state) => state.userData)

  const onSearch = useCallback(
    (searchString: string) => {
      navigate({
        search: {
          ...search,
          search: searchString,
        },
      })

      window.scrollTo({ top: 0 })
    },
    [navigate],
  )

  const onChange = useCallback(
    (page: number) => {
      navigate({
        search: {
          page,
        },
      })

      window.scrollTo({ top: 0 })
    },
    [navigate],
  )

  return (
    <Box>
      <TitleRow title="Events">
        {hasPermission(userInfo, "global", "createEvent") && (
          <Box>
            <PartyLink to={CREATE_EVENT_ROUTE} variant="outlined">
              Create Event
            </PartyLink>
          </Box>
        )}
      </TitleRow>
      <Box
        display="flex"
        flexDirection="row"
        gap={2}
        alignItems="center"
        justifyContent="center"
      >
        <EventFilter from={EVENTS_ROUTE} search={search} />
        <EventSearch search={search.search} onSearch={onSearch} />
      </Box>
      {events && (
        <EventsList
          search={search.search}
          events={events}
          onPageChange={onChange}
          page={search.page}
        />
      )}
    </Box>
  )
}
