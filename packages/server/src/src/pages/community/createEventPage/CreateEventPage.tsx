import { Box } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useEffect, useState } from "react"

import { EventConfiguration } from "../../../components/events/EventConfiguration/EventConfiguration"
import { hasPermission } from "../../../permissions"
import { COMMUNITIES_ROOT_ROUTE, EVENT_ROUTE_INFO } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import {
  isCommunity,
  useParentRouteData,
} from "../../../utils/pages.rootObject"

import * as styles from "./createEventPage.module.css"

export const CreateEventPage = () => {
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)
  const [created, setCreated] = useState<null | number>(null)
  const user = useUserStore((state) => state.userData)
  const navigate = useNavigate()

  if (!isCommunity(base)) {
    return null
  }

  if (!hasPermission(user, "community", "event", base)) {
    return null
  }

  useEffect(() => {
    if (created !== null) {
      navigate({
        to: EVENT_ROUTE_INFO,
        params: {
          eventId: String(created),
        },
      })
    }
  }, [created])

  return (
    <Box className={styles.container}>
      <EventConfiguration communityId={base.id} onSuccess={setCreated} />
    </Box>
  )
}
