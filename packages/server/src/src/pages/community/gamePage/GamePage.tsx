import { <PERSON>, Button, Typography } from "@mui/material"
import { useN<PERSON><PERSON>, useRouter } from "@tanstack/react-router"
import { useCallback, useMemo } from "react"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { BggLink } from "../../../components/games/BggLink/BggLink"
import { GameInfo } from "../../../components/games/GameInfo/GameInfo"
import { GAME_IMAGES } from "../../../config/images"
import { gameRoute } from "../../../routes/community/game.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_ROUTE,
  COMMUNITY_USER_ROUTE,
  GAMES_ROUTE,
  GAME_ROUTE,
} from "../../../routes/paths"
import {
  isCommunity,
  useParentRouteData,
} from "../../../utils/pages.rootObject"

import { Chip<PERSON> } from "./components/Chips"
import { ExpansionList } from "./components/ExpansionList"
import { Owners } from "./components/Owners"
import * as styles from "./gamePage.module.css"

const backLinks: Record<string, string> = {
  games: GAMES_ROUTE,
  community: COMMUNITY_ROUTE,
  user: COMMUNITY_USER_ROUTE,
}

export const GamePage = () => {
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)
  const game = gameRoute.useLoaderData()
  const search = gameRoute.useSearch()
  const params = gameRoute.useParams()
  const navigate = useNavigate()

  const router = useRouter()

  const passedParams: Record<string, string> = useMemo(() => {
    return search.sourceParams ? JSON.parse(search.sourceParams) : {}
  }, [search.sourceProps])

  const onViewUser = useCallback(
    (id?: number) =>
      navigate({
        to: GAME_ROUTE,
        params: {
          communityId: params.communityId,
          gameId: params.gameId,
        },
        search: {
          userId: id,
          hideOthers: search.hideOthers,
        },
      }),
    [navigate, params, search],
  )

  const handleBack = useCallback(() => {
    navigate({
      to: backLinks[search.sourcePage ?? "community"],
      params: {
        communityId: params.communityId,
        ...passedParams,
      },
      search: search.sourceProps ? JSON.parse(search.sourceProps) : {},
    })
  }, [router])

  if (!isCommunity(base) || !game) return null

  return (
    <Box pb={8}>
      <TitleRow
        title={
          <Box display="flex" flexDirection="row" gap={2}>
            <Button variant="text" onClick={handleBack}>
              {`<- Back`}
            </Button>
            {game.game.title}
          </Box>
        }
      ></TitleRow>
      <Box className={styles.firstRowWrapper}>
        <Box className={styles.gameInfoWrapper}>
          <Box display="flex" justifyContent="center">
            <Box className={styles.imageBox}>
              <img
                src={`${ENV_IMAGE_CDN}${GAME_IMAGES}/${game.game.id}.jpg`}
                alt={game.game.title}
              />
              <Box className={styles.link}>
                <BggLink bggId={game.game.bggId} />
                {game.game.average && (
                  <Box className={styles.bggRating}>
                    <Typography fontWeight={600}>
                      {Math.round(game.game.average * 10) / 10}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
          </Box>
          <GameInfo game={game.game} />
        </Box>
        <Owners
          communityId={base?.id ?? 0}
          search={search}
          users={game.game.users}
          onViewUser={onViewUser}
        />
      </Box>
      <Box className={styles.borderBox} mt={2}>
        <Chips tagList={game.tags} />
      </Box>
      {game.expansions.length > 0 && (
        <Box pt={2} className={styles.borderBox}>
          <ExpansionList
            users={game.game.users}
            expansions={game.expansions}
            onViewUser={onViewUser}
          />
        </Box>
      )}
    </Box>
  )
}
