import PersonOutlineOutlinedIcon from "@mui/icons-material/PersonOutlineOutlined"
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined"
import { Box, Button, ButtonGroup } from "@mui/material"

import { gameRoute } from "../../../../routes/community/game.route"
import { useUserStore } from "../../../../store/useUserStore"

export interface ICChipUser {
  id: number
  name: string
  color: string | null
  avatar: string | null
}

interface UserChipProps {
  tagList: ICChipUser[]
  onViewUser: (user?: number) => void
  onOpenUserProfile: (user: number) => void
}

export const UserChips = ({
  tagList,
  onViewUser,
  onOpenUserProfile,
}: UserChipProps) => {
  const myUserId = useUserStore((state) => state.userData.id)
  const { userId } = gameRoute.useSearch()

  const sortTags = (t1: ICChipUser, t2: ICChipUser) => {
    if (t1.id === myUserId) return -1
    if (t2.id === myUserId) return 1

    return t1.name < t2.name ? -1 : 1
  }

  return (
    <Box
      padding={2}
      gap={0.5}
      display="flex"
      flexDirection="row"
      flexWrap="wrap"
    >
      <ButtonGroup title="All Users">
        <Button
          title="All Users"
          startIcon={<VisibilityOutlinedIcon />}
          variant={!userId ? "contained" : "outlined"}
          onClick={() => onViewUser()}
        >
          All users
        </Button>
      </ButtonGroup>
      {tagList.sort(sortTags).map((tag) => (
        <ButtonGroup key={tag.id} title={tag.name}>
          <Button
            title="Filter user info"
            startIcon={<VisibilityOutlinedIcon />}
            variant={tag.id === userId ? "contained" : "outlined"}
            onClick={() => onViewUser(tag.id)}
          >
            {tag.name}
          </Button>
          {tag.id !== myUserId && (
            <Button
              title="Open users profile"
              variant={tag.id === userId ? "contained" : "outlined"}
              endIcon={<PersonOutlineOutlinedIcon />}
              onClick={() => onOpenUserProfile(tag.id)}
            />
          )}
        </ButtonGroup>
      ))}
    </Box>
  )
}
