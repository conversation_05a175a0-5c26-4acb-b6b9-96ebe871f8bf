import { useNavigate } from "@tanstack/react-router"

import { ChipsList } from "../../../../components/games/ChipsList/ChipsList"
import { gameRoute } from "../../../../routes/community/game.route"
import { GAMES_ROUTE } from "../../../../routes/paths"

import type { IGameViewTag } from "../../../../types/tRPC.types"

interface ChipListProps {
  tagList: IGameViewTag[]
}

export const Chips = ({ tagList }: ChipListProps) => {
  const navigate = useNavigate()
  const linkParams = gameRoute.useParams()

  const openTag = (tag: IGameViewTag) =>
    navigate({
      to: GAMES_ROUTE,
      params: {
        communityId: linkParams.communityId,
      },
      search: {
        search: tag.title,
        orderBy: "asc",
        page: 1,
        order: "search",
      },
    })

  return <ChipsList tagList={tagList} onOpenTag={openTag} />
}
