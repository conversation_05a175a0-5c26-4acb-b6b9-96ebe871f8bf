import { Box } from "@mui/material"
import { useRouter } from "@tanstack/react-router"
import React, { useEffect } from "react"

import { CommunityConfiguration } from "../../../components/community/CommunityConfiguration/CommunityConfiguration"
import { CommunityUploadImage } from "../../../components/community/CommunityUploadImage/CommunityUploadImage"
import { hasPermission } from "../../../permissions"
import { COMMUNITIES_ROOT_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import {
  isCommunity,
  useParentRouteData,
} from "../../../utils/pages.rootObject"

import * as styles from "./editCommunityPage.module.css"

export const EditCommunityPage = () => {
  const [saved, setSaved] = React.useState(false)
  const router = useRouter()
  const [savedImage, setSavedImage] = React.useState<number>(0)
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)

  useEffect(() => {
    if (savedImage > 0) {
      router.invalidate()
    }
  }, [savedImage, router])

  const user = useUserStore((state) => state.userData)

  if (!isCommunity(base)) {
    return null
  }

  if (!hasPermission(user, "community", "update", { id: base.id })) {
    return null
  }

  return (
    <Box className={styles.container}>
      <CommunityUploadImage
        id={base.id}
        onSuccess={() => setSavedImage(savedImage + 1)}
        hasImage={base.image}
        notNew={true}
      />
      <CommunityConfiguration edit={base} onSuccess={() => setSaved(true)} />
    </Box>
  )
}
