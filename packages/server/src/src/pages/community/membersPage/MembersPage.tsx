import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { UserList } from "../../../components/user/UserList/UserList"
import { membersRoute } from "../../../routes/community/members.route"
import { COMMUNITIES_ROOT_ROUTE } from "../../../routes/paths"
import {
  isCommunity,
  useParentRouteData,
} from "../../../utils/pages.rootObject"

export const MembersPage = () => {
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)
  const users = membersRoute.useLoaderData()

  if (!isCommunity(base) || !users) return null

  return (
    <>
      <TitleRow title="Members" />
      <UserList
        users={users}
        communityId={base.id.toString()}
        labelInfo="Member: "
      />
    </>
  )
}
