import { Box } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback, useEffect, useMemo } from "react"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import {
  GameSearch,
  type SearchParams,
} from "../../../components/games/GameSearch/GameSearch"
import { GamesThumbnailView } from "../../../components/games/GamesThumbnailView/GamesThumbnailView"
import { SearchModal } from "../../../components/modals/SearchModal/SearchModal"
import { gamesRoute } from "../../../routes/community/games.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_USER_ROUTE,
  GAMES_ROUTE,
  GAME_ROUTE,
} from "../../../routes/paths"
import { isOrderBy, useGameStore } from "../../../store/useGamesStore"
import { useIsMobileStore } from "../../../store/useIsMobileStore"
import { applyFilters } from "../../../utils/filter"
import {
  isCommunity,
  useParentRouteData,
} from "../../../utils/pages.rootObject"

import * as styles from "./gamesPage.module.css"

export const GamesPage = () => {
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)

  const search = gamesRoute.useSearch()
  const linkParams = gamesRoute.useParams()
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  const { getGames, setMeta } = useGameStore()

  const games = getGames(parseInt(linkParams.communityId))

  const navigate = useNavigate({ from: GAMES_ROUTE })

  useEffect(() => {
    const order = search.order === "desc" ? "desc" : "asc"
    const orderBy =
      search.orderBy && isOrderBy(search.orderBy) ? search.orderBy : "title"
    setMeta({
      search: search.search ?? "",
      order,
      minPlayers: search.minPlayers,
      maxPlayers: search.maxPlayers,
      playerLevel: search.playerLevel ?? 1,
      orderBy,
      communityId: parseInt(linkParams.communityId),
    })
  }, [search, linkParams.communityId])

  const onClick = useCallback(
    (id?: number) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params: {
          userId: String(id),
        },
      }),
    [navigate],
  )

  if (!isCommunity(base) || !games) return null

  const isLargeTablet = sizeThresholdList.largeTablet

  const useGameList = useMemo(() => {
    return applyFilters(
      games.games,
      games.meta,
      games.populatedTags,
      games.populatedUsers,
    )
  }, [games])

  const onNavigateGame = useCallback(
    (search: SearchParams) => {
      navigate({
        to: GAMES_ROUTE,
        search,
        params: linkParams,
      })
    },
    [navigate, linkParams],
  )

  const onChange = useCallback(
    (page: number) => {
      navigate({
        search: {
          page,
          minPlayers: games.meta.minPlayers,
          maxPlayers: games.meta.maxPlayers,
          playerLevel: games.meta.playerLevel,
          search: games.meta.search ?? "",
          order: games.meta.order,
          orderBy: games.meta.orderBy,
        },
      })

      window.scrollTo({ top: 0 })
    },
    [games.meta, navigate],
  )

  const titleRow = useMemo(() => {
    return <TitleRow title="Games" />
  }, [linkParams.communityId])

  return (
    <>
      <Box className={styles.header}>
        <Box className={styles.searchBar}>
          <GameSearch
            onNavigate={onNavigateGame}
            search={search}
            tags={games.tags}
            tagCategories={games.tagCategories}
          />
          <SearchModal
            onNavigate={onNavigateGame}
            search={search}
            tags={games.tags}
            tagCategories={games.tagCategories}
          />
        </Box>
        {titleRow}
      </Box>
      <GamesThumbnailView
        listMode={isLargeTablet}
        navigation={{
          to: GAME_ROUTE,
          params: {
            communityId: linkParams.communityId.toString(),
          },
          search: {
            sourcePage: "games",
            sourceProps: JSON.stringify(search),
          },
        }}
        games={useGameList}
        onUser={onClick}
        onPageChange={onChange}
        page={search.page ?? 1}
      />
    </>
  )
}
