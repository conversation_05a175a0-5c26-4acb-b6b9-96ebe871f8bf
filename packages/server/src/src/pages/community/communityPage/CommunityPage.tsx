import { Box, Typography } from "@mui/material"

import { communityOpenRoute } from "../../../routes/community/community.route"
import { COMMUNITIES_ROOT_ROUTE } from "../../../routes/paths"
import { useIsMobileStore } from "../../../store/useIsMobileStore"
import {
  isCommunity,
  useParentRouteData,
} from "../../../utils/pages.rootObject"

import * as styles from "./community.module.css"
import { EventView } from "./components/EventView"
import { GameList } from "./components/GameList"
import { Header } from "./components/Header"
import { UserList } from "./components/UserList"
import { Welcome } from "./components/Welcome"

export const CommunityPage = () => {
  const community = communityOpenRoute.useLoaderData()
  const linkParams = communityOpenRoute.useParams()
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)

  if (!isCommunity(base) || !base || !community) {
    return <Typography>Loading data</Typography>
  }

  const isLargeTablet = sizeThresholdList.largeTablet

  const isWelcome = (base.welcome?.length ?? 0) > 7

  return (
    <>
      {isLargeTablet && (
        <Box pt={2}>
          <Typography variant="h4">
            Welcome to{" "}
            <Box component="span" fontWeight="bold">
              {base?.name ?? ""}
            </Box>
          </Typography>
        </Box>
      )}
      <Header />
      {isWelcome && (
        <Box className={styles.welcome}>
          <Welcome />
        </Box>
      )}
      <Box py={8} gap={8} display="flex" flexDirection="column">
        {community.events.length > 0 && (
          <EventView
            events={community.events}
            communityId={linkParams.communityId}
          />
        )}
        <GameList />
        <UserList />
      </Box>
    </>
  )
}
