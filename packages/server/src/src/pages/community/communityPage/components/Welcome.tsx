import { Box } from "@mui/material"

import { COMMUNITIES_ROOT_ROUTE } from "../../../../routes/paths"
import {
  isCommunity,
  useParentRouteData,
} from "../../../../utils/pages.rootObject"

export const Welcome = () => {
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)

  if (!isCommunity(base)) {
    return null
  }

  return (
    <Box
      width="100%"
      pt={2}
      mb={2}
      borderTop="1px solid #ccc"
      borderBottom="1px solid #ccc"
    >
      <Box
        p={4}
        pt={8}
        width="100%"
        pb={8}
        boxSizing="border-box"
        maxWidth="1000px"
        margin="auto"
        lineHeight="2em"
        textAlign="center"
      >
        <div dangerouslySetInnerHTML={{ __html: base?.welcome ?? "" }} />
      </Box>
    </Box>
  )
}
