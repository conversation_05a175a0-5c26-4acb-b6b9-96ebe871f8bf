@value isLargeTablet, isTablet from "../../../../css/size.module.css";

.title {
  padding: var(--spacing-2);
  margin-top: var(--spacing-1);
  margin-left: var(--spacing-15);
}

@media (max-width: isTablet) {
  .title {
    margin-top: 0;
    margin-left: var(--spacing-2);
  }
}

.titleBox {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  gap: var(--spacing-2);
}

@media (max-width: isTablet) {
  .titleBox {
    flex-direction: row-reverse;
    align-items: flex-start;
  }
}

.itemsBox {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: var(--spacing-2);
}

@media (max-width: isTablet) {
  .itemsBox {
    flex-direction: column;
  }
}

.location {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-h);
}

.membershipStatus {
  display: flex;
  justify-content: flex-end;
}
