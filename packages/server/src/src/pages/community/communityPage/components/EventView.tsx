import { Box, Card, Typography } from "@mui/material"
import classnames from "classnames"

import { SkipLink } from "../../../../components/elements/SkipLink/SkipLink"
import { PartyLink } from "../../../../components/elements/link/PartyLink/PartyLink"
import {
  COMMUNITY_EVENTS_ROUTE,
  EVENT_ROUTE_INFO,
} from "../../../../routes/paths"
import { localToLocalWithDay } from "../../../../utils/transformTime"

import * as styles from "./eventView.module.css"

interface Event {
  id: number
  title: string
  starts: string
  role: string | null
}
interface EventViewProps {
  events: Event[]
  communityId: string
}
export const EventView = ({ events, communityId }: EventViewProps) => {
  return (
    <Box>
      <SkipLink goTo="#games" title="Skip to games" />
      <Box>
        <Typography variant="h5">Upcoming Events</Typography>
        <PartyLink
          to={COMMUNITY_EVENTS_ROUTE}
          params={{ communityId: communityId }}
        >
          See all events
        </PartyLink>
      </Box>
      <Box>
        {events.map((event) => (
          <PartyLink
            to={EVENT_ROUTE_INFO}
            key={event.id}
            params={{ eventId: String(event.id) }}
          >
            <Card
              className={classnames(styles.card, {
                [styles.participant]: event.role !== null,
                [styles.host]: event.role === "host" || event.role === "cohost",
              })}
            >
              <Typography variant="h6">{event.title}</Typography>
              <Typography variant="body2">
                {localToLocalWithDay(event.starts, true)}
              </Typography>
              <Typography variant="body2">{event.role}</Typography>
            </Card>
          </PartyLink>
        ))}
      </Box>
    </Box>
  )
}
