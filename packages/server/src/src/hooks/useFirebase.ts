import {
  GoogleAuthProvider,
  type User,
  createUserWithEmailAndPassword,
  getAuth,
  getIdTokenResult,
  signInWithEmailAndPassword,
  signInWithPopup,
  signOut,
} from "firebase/auth"
import { useCallback, useEffect, useState } from "react"

import { router } from "../routes/router"
import { auth as authStorage } from "../utils/access"
import { firebaseApp } from "../utils/firebase"

export const useFirebase = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [userExists, setUserExists] = useState<boolean | null>(null)
  const [getAccessTokenSilently, setGetAccessTokenSilently] = useState(
    () => async (): Promise<string | null> => null,
  )

  useEffect(() => {
    const refresh = async () => {
      if (user) {
        const tokenResult = await getIdTokenResult(user)
        const expirationTime = new Date(tokenResult.expirationTime)

        if (expirationTime < new Date()) {
          await refreshToken()
        }
      }
    }

    refresh()
  }, [user])

  useEffect(() => {
    const firebaseAuth = getAuth(firebaseApp)
    const unsubscribe = firebaseAuth.onAuthStateChanged(async (user) => {
      if (user) {
        setIsAuthenticated(true)
        setGetAccessTokenSilently(() => async () => user.getIdToken())
        setUserExists(true)
      } else {
        setIsAuthenticated(false)
        setGetAccessTokenSilently(() => async () => null)
        setUserExists(false)
      }

      setUser(user)
    })

    return () => unsubscribe()
  }, [])

  const loginWithGoogle = useCallback(async () => {
    const firebaseAuth = getAuth(firebaseApp)
    const provider = new GoogleAuthProvider()
    try {
      await signInWithPopup(firebaseAuth, provider)
    } catch (error) {
      console.error("Login failed:", error)
    }
  }, [])

  const loginWithEmail = useCallback(
    async (email: string, password: string) => {
      const firebaseAuth = getAuth(firebaseApp)
      try {
        await signInWithEmailAndPassword(firebaseAuth, email, password)
      } catch (error) {
        console.error("Login failed:", error)
      }
    },
    [],
  )

  const logout = useCallback(async () => {
    const firebaseAuth = getAuth(firebaseApp)
    try {
      await signOut(firebaseAuth)
      await router.invalidate()
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }, [])

  const refreshToken = useCallback(async () => {
    if (user) {
      authStorage.authToken = await user.getIdToken(true)
    }
  }, [user])

  const register = async (email: string, password: string) => {
    const firebaseAuth = getAuth(firebaseApp)
    try {
      await createUserWithEmailAndPassword(firebaseAuth, email, password)
    } catch (error) {
      console.error("Register failed:", error)
    }
  }

  return {
    isAuthenticated,
    getAccessTokenSilently,
    user,
    loginWithGoogle,
    loginWithEmail,
    logout,
    refreshToken,
    register,
    userExists,
  }
}
