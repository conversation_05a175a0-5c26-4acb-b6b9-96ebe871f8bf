import { useCallback } from "react"

import { router } from "../routes/router"
import { trpc } from "../trpc/trpc"
import { IEventBaseData } from "../types/tRPC.types"

import { useUserDataLoader } from "./useUserDataLoader"

export const useEvent = () => {
  const { reFetch } = useUserDataLoader()

  const onChangeUserState = useCallback(
    async ({
      event,
      status,
      onComplete,
    }: {
      event: Pick<IEventBaseData, "id">
      onComplete: (role: string | null) => void
      status:
        | "participant"
        | "interested"
        | "requested"
        | "reserved"
        | "notgoing"
    }) => {
      try {
        const result = await trpc.changeMyEventStatus.mutate({
          eventId: event.id,
          status,
        })

        onComplete(result.role)
        await router.invalidate()
        await reFetch()
      } catch (error) {
        console.error(error)
      }
    },
    [reFetch],
  )

  return { onChangeUserState }
}
