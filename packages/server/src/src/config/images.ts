// src={`${ENV_IMAGE_CDN}/games/images/${game.game.id}.jpg`}

export const GAME_IMAGES = "/games/images"

export const PROFILE_IMAGES = "/profilePictures"

export const COMMUNITY_IMAGES = "/community/logo"

export const EVENT_IMAGES = "/event/main"

export const IMAGES_LARGE = "large_"
export const IMAGES_SMALL = "small_"
export const IMAGES_COVER = "largeCover_"

export const defaultCommunityLogo = `${ENV_IMAGE_CDN}/${ENV_COMMUNITY_DEFAULT_LOGO}`
export const defaultEventImage = `${ENV_IMAGE_CDN}/event/main/default_{size}{id}.png`

// Does not exist yet
export const defaultGameImage = `${ENV_IMAGE_CDN}/games/images/default_game_image.png`

// Does not exist yet
export const defaultProfileImage = `${ENV_IMAGE_CDN}/profilePictures/default_profile_image.png`

const imageTypes = {
  community: COMMUNITY_IMAGES,
  event: EVENT_IMAGES,
  game: GAME_IMAGES,
  profile: PROFILE_IMAGES,
}

const imageSizes = {
  large: IMAGES_LARGE,
  small: IMAGES_SMALL,
  cover: IMAGES_COVER,
}

const defaultImages: Record<string, string> = {
  community: defaultCommunityLogo,
  event: defaultEventImage,
  game: defaultGameImage,
  profile: defaultProfileImage,
}

export const createDefaultImageLink = (
  type: keyof typeof defaultImages,
  id: number,
  size: keyof typeof imageSizes,
) => {
  return defaultImages[type]
    .replace("{id}", id.toString())
    .replace("{size}", imageSizes[size])
}

export const createImageLink = (
  type: keyof typeof imageTypes,
  size: keyof typeof imageSizes,
  id: number,
  image?: string | null,
) => {
  return image
    ? `${ENV_IMAGE_CDN}${imageTypes[type]}/${imageSizes[size]}${image}`
    : createDefaultImageLink(type, id, size)
}
