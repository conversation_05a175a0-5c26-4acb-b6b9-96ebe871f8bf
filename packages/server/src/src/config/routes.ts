// TODO: fix second for accurete stale times
const SECOND = 1 //1000

export const NO_CACHE_STALE_TIME = 0
export const CHANGING_LISTS_STALE_TIME = 10 * SECOND
export const SLOW_CACHE_STALE_TIME = 60 * 60 * SECOND // 1 hour
export const MEDIUM_CACHE_STALE_TIME = 60 * 30 * SECOND // 30 minutes
export const FAST_CACHE_STALE_TIME = 60 * 10 * SECOND // 10 minutes

/* Replace originals with keys */
export const COMMUNITIES_STALE_TIME = CHANGING_LISTS_STALE_TIME // 10 minutes
export const EVENT_STALE_TIME = NO_CACHE_STALE_TIME
export const TAG_LIST_STALE_TIME = SLOW_CACHE_STALE_TIME // 10 minutes
export const COMMUNITY_STALE_TIME = FAST_CACHE_STALE_TIME // 10 minutes
export const COMMUNITY_GAMES_STALE_TIME = NO_CACHE_STALE_TIME
export const COMMUNITY_GAME_STALE_TIME = SLOW_CACHE_STALE_TIME // 10 minutes
export const USER_PROFILE_STALE_TIME = FAST_CACHE_STALE_TIME // 10 minutes
