import { GrowthBookProvider } from "@growthbook/growthbook-react"
import { StyledEngineProvider, ThemeProvider } from "@mui/material"
import { APIProvider } from "@vis.gl/react-google-maps"
import dayjs from "dayjs"
import localizedFormat from "dayjs/plugin/localizedFormat"
import utc from "dayjs/plugin/utc"
import isMobile from "is-mobile"
import { useEffect } from "react"

import { MAPS_API_KEY } from "./config/maps"
import { useFirebase } from "./hooks/useFirebase"
import { useUserDataLoader } from "./hooks/useUserDataLoader"
import { RouterProviderWithContext } from "./routes/router"
import { useIsMobileStore } from "./store/useIsMobileStore"
import { useUserStore } from "./store/useUserStore"
import { theme } from "./theme"
import { growthbook } from "./utils/growthBook"
import "./app.module.css"

dayjs.extend(utc)
dayjs.extend(localizedFormat)
export const App = () => {
  useFirebase()
  useUserDataLoader()

  const { userLoaded } = useUserStore()
  const { setIsMobile, setCurrentWidth } = useIsMobileStore()

  useEffect(() => {
    // Load features asynchronously when the app renders
    growthbook.init({ streaming: true })
  }, [])

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(isMobile())
      setCurrentWidth(window.innerWidth)
    }
    handleResize()
    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [setIsMobile])

  return (
    <GrowthBookProvider growthbook={growthbook}>
      <APIProvider apiKey={MAPS_API_KEY} libraries={["marker", "places"]}>
        <StyledEngineProvider injectFirst>
          <ThemeProvider theme={theme} defaultMode="light">
            {userLoaded && <RouterProviderWithContext />}
          </ThemeProvider>
        </StyledEngineProvider>
      </APIProvider>
    </GrowthBookProvider>
  )
}
