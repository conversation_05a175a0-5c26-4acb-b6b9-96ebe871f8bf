import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { USER_PROFILE_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { UsersProfilePage } from "../../pages/index/usersProfilePage/UsersProfilePage"
import { filterGamesSearchSchema } from "../../schemas"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PROFILE_ROUTE } from "../paths"

import { indexRootRoute } from "./index.root.route"

const tabsSchema = z.object({
  tab: z.string().optional().catch("profile"),
})

export const userProfileRouteSearchSchema = tabsSchema.extend(
  filterGamesSearchSchema.shape,
)

export const usersProfileRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  validateSearch: (search) => userProfileRouteSearchSchema.parse(search),
  path: PROFILE_ROUTE,
  staleTime: USER_PROFILE_STALE_TIME,
  pendingComponent: LoadingPage,
  pendingMs: 300,
  loader: async ({ context: { trpc } }) => {
    try {
      return await trpc.getMyInfo.query()
    } catch (error) {
      return handleLoaderErrors("User information not available", error)
    }
  },
  component: UsersProfilePage,
})
