import { createRoute } from "@tanstack/react-router"

import { LOGGEDIN_ROUTE, LOGGEDOUT_ROUTE } from "../paths"

import { indexRootRoute } from "./index.root.route"

export const loggedinRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  path: LOGGEDIN_ROUTE,
  component: function Index() {
    return <div>You logged In</div>
  },
})

export const loggedoutRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  path: LOGGEDOUT_ROUTE,
  component: function Index() {
    return <div>You logged Out!</div>
  },
})
