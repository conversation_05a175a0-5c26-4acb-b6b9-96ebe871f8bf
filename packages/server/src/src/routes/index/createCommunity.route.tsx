import { createRoute } from "@tanstack/react-router"

import { CreateCommunityPage } from "../../pages/index/createCommunityPage/CreateCommunityPage"
import { CREATE_COMMUNITY_ROUTE } from "../paths"

import { indexRootRoute } from "./index.root.route"

export const createCommunityRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  path: CREATE_COMMUNITY_ROUTE,
  component: CreateCommunityPage,
})
