import { createRoute } from "@tanstack/react-router"

import { COMMUNITIES_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { PublicCommunitiesPage } from "../../pages/index/publicCommunitiesPage/PublicCommunitiesPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PUBLIC_COMMUNITIES_ROUTE } from "../paths"

import { indexRootRoute } from "./index.root.route"

export const publicCommunitiesRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  staleTime: COMMUNITIES_STALE_TIME,
  path: PUBLIC_COMMUNITIES_ROUTE,
  loader: async ({ context: { trpc } }) => {
    try {
      return await trpc.publicCommunityList.query()
    } catch (error) {
      return handleLoaderErrors("Can't find public communities", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: PublicCommunitiesPage,
})
