import { createRoute } from "@tanstack/react-router"

import { NO_CACHE_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { CreateEventPage } from "../../pages/index/createEventPage/CreateEventPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { CREATE_EVENT_ROUTE } from "../paths"

import { indexRootRoute } from "./index.root.route"

export const createPrivateEventRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  staleTime: NO_CACHE_STALE_TIME,
  path: CREATE_EVENT_ROUTE,
  loader: async ({ context: { trpc } }) => {
    try {
      // Fetch ecents
    } catch (error) {
      return handleLoaderErrors("Can't find events", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: CreateEventPage,
})
