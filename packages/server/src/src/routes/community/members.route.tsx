import { createRoute } from "@tanstack/react-router"

import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { MembersPage } from "../../pages/community/membersPage/MembersPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_COMMUNITY_USERS_ROUTE } from "../paths"

import { communityRootRoute } from "./community.root.route"

export const membersRoute = createRoute({
  getParentRoute: () => communityRootRoute,
  path: PART_COMMUNITY_USERS_ROUTE,
  pendingComponent: LoadingPage,
  pendingMs: 300,
  loader: async ({ context: { trpc }, params: { communityId } }) => {
    try {
      return await trpc.communityUserList.query({
        communityId: parseInt(communityId),
      })
    } catch (error) {
      return handleLoaderErrors(
        "You don't have access to community users",
        error,
      )
    }
  },
  component: MembersPage,
})
