import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { MemberPage } from "../../pages/community/memberPage/MemberPage"
import { filterGamesSearchSchema } from "../../schemas"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_COMMUNITY_USER_ROUTE } from "../paths"

import { communityRootRoute } from "./community.root.route"

const tabsSchema = z.object({
  tab: z.string().optional().catch("profile"),
})

export const userRouteSearchSchema = tabsSchema.extend(
  filterGamesSearchSchema.shape,
)

export const memberRoute = createRoute({
  getParentRoute: () => communityRootRoute,
  path: PART_COMMUNITY_USER_ROUTE,
  validateSearch: (search) => userRouteSearchSchema.parse(search),
  pendingComponent: LoadingPage,
  pendingMs: 300,
  loader: async ({ context: { trpc }, params: { communityId, userId } }) => {
    try {
      return await trpc.communityUserInfo.query({
        communityId: parseInt(communityId),
        userId: parseInt(userId),
      })
    } catch (error) {
      return handleLoaderErrors("User not found", error)
    }
  },
  component: MemberPage,
})
