import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { EVENT_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { EventOrganizersPage } from "../../pages/event/eventOrganizersPage/EventOrganizersPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_ORGANIZERS_GAME_ROUTE } from "../paths"

import { eventRootRoute } from "./event.root.route"

const eventInfoSchema = z.object({})

export const eventOrganizersRoute = createRoute({
  validateSearch: (search) => eventInfoSchema.parse(search),
  getParentRoute: () => eventRootRoute,
  path: PART_ORGANIZERS_GAME_ROUTE,
  staleTime: EVENT_STALE_TIME,
  loader: async ({ context: { trpc }, params: { eventId } }) => {
    try {
      return await trpc.eventOrganizers.query({
        eventId: parseInt(eventId),
      })
    } catch (error) {
      return handleLoaderErrors("Can't find games", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: EventOrganizersPage,
})
