import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { EVENT_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { EventParticipantsPage } from "../../pages/event/eventParticipantsPage/EventParticipantsPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_EVENT_PARTICIPANTS_ROUTE } from "../paths"

import { eventRootRoute } from "./event.root.route"

const eventInfoSchema = z.object({
  tab: z.string().optional(),
})

export const eventParticipantsRoute = createRoute({
  validateSearch: (search) => eventInfoSchema.parse(search),
  getParentRoute: () => eventRootRoute,
  path: PART_EVENT_PARTICIPANTS_ROUTE,
  staleTime: EVENT_STALE_TIME,
  loader: async ({ context: { trpc }, params: { eventId } }) => {
    try {
      return await trpc.eventParticipantList.query({
        eventId: parseInt(eventId),
      })
    } catch (error) {
      return handleLoaderErrors("Can't find participants", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: EventParticipantsPage,
})
