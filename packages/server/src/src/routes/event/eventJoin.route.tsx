import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { EVENT_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { EventInfoPage } from "../../pages/event/eventInfoPage/EventInfoPage"
import { PART_EVENT_JOIN_ROUTE } from "../paths"

import { eventRootRoute } from "./event.root.route"

const eventJoinSchema = z.object({
  step: z.string().optional(),
})

export const eventJoinRoute = createRoute({
  validateSearch: (search) => eventJoinSchema.parse(search),
  getParentRoute: () => eventRootRoute,
  path: PART_EVENT_JOIN_ROUTE,
  staleTime: EVENT_STALE_TIME,
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: EventInfoPage,
})
