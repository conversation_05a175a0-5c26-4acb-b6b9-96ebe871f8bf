import { notFound } from "@tanstack/react-router"

import { isTRPCClientError } from "../trpc/trpc"

export interface ErrorMessage {
  message: string
  code: string
}

export function isErrorMessage(error: unknown): error is ErrorMessage {
  return (error as ErrorMessage)?.message !== undefined
}

export const handleLoaderErrors = (
  message: string = "Failed to load data",
  error: unknown,
) => {
  const data: ErrorMessage = {
    message,
    code: isTRPCClientError(error) && error.data ? error.data.code : "UNKNOWN",
  }

  throw notFound({
    data,
  })

  return undefined
}
