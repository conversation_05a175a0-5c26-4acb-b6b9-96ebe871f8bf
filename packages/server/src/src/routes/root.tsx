import { Outlet, createRootRouteWithContext } from "@tanstack/react-router"
import { type FirebaseApp } from "firebase/app"

import { type UseGameStoreProps } from "../store/useGamesStore"
import { type UseUserStoreProps } from "../store/useUserStore"
import { trpc } from "../trpc/trpc"

interface MyRouterContext {
  trpc: typeof trpc
  auth: FirebaseApp | null
  userData: null | UseUserStoreProps
  gameStore: null | UseGameStoreProps
}

export const rootRoute = createRootRouteWithContext<MyRouterContext>()({
  component: Outlet,
})
