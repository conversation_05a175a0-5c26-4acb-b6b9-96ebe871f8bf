/* eslint import/order: 0 */
/* eslint import/named: 0 */
/*
import { type CreateTRPCReact, createTRPCReact } from "@trpc/react-query"

//ts-ignore
import type { AppRouter } from "../../../router/root/queries/router"
// import type { AppRouter } from "../../../../api/src/root/queries/router"

// export const trpc = createTRPCReact<AppRouter>()

export const trpc: CreateTRPCReact<AppRouter, unknown> =
  createTRPCReact<AppRouter>()
 */

// eslint-ignore-next-line import/order
import { createTRPCClient, httpBatchLink, TRPCClientError } from "@trpc/client"

//ts-ignore
import type { inferRouterInputs, inferRouterOutputs } from "@trpc/server"

import { auth } from "../utils/access"

import type { AppRouter } from "../../../../api/src/root/router"

export const trpc = createTRPCClient<AppRouter>({
  links: [
    httpBatchLink({
      url: `${ENV_API_URL}/trpc`,
      headers: () => {
        return {
          Authorization: `Bearer ${auth.authToken}`,
        }
      },
    }),
  ],
})

export type ArrayElement<A> = A extends readonly (infer T)[] ? T : never

export type RouterInput = inferRouterInputs<AppRouter>
export type RouterOutput = inferRouterOutputs<AppRouter>

export const protectedProcedure = ""

export function isTRPCClientError(
  cause: unknown,
): cause is TRPCClientError<AppRouter> {
  return cause instanceof TRPCClientError
}
