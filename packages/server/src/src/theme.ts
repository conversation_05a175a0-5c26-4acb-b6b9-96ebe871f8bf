import { createTheme } from "@mui/material/styles"

// small helper: returns either '#ffffff' or a dark color depending on contrast
function hexToRgb(hex: string) {
  const h = hex.replace("#", "")
  const bigint = parseInt(h, 16)
  return [(bigint >> 16) & 255, (bigint >> 8) & 255, bigint & 255]
}

function relativeLuminance(hex: string) {
  const [r, g, b] = hexToRgb(hex)
    .map((v) => v / 255)
    .map((c) => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    })
  return 0.2126 * r + 0.7152 * g + 0.0722 * b
}

function contrastRatio(lum1: number, lum2: number) {
  return (Math.max(lum1, lum2) + 0.05) / (Math.min(lum1, lum2) + 0.05)
}

function contrastColor(hex: string) {
  // Aim for at least 6:1 contrast ratio (WCAG AAA for normal text)
  const targetLum = relativeLuminance(hex)
  const whiteLum = relativeLuminance("#ffffff")
  const darkHex = "#0b2540"
  const darkLum = relativeLuminance(darkHex)

  const contrastWithWhite = contrastRatio(whiteLum, targetLum)
  const contrastWithDark = contrastRatio(darkLum, targetLum)

  // ensure minimum 6:1 ratio
  if (contrastWithWhite >= 6) return "#ffffff"
  if (contrastWithDark >= 6) return darkHex

  // fallback: pick the higher contrast even if <6
  return contrastWithWhite >= contrastWithDark ? "#ffffff" : darkHex
}

export const MUIPrimaryMainColor = "#6EA8C0" // user provided

export const theme = createTheme({
  palette: {
    mode: "light",
    primary: {
      main: MUIPrimaryMainColor,
      light: "#97c6d6",
      dark: "#457a8c",
      contrastText: contrastColor(MUIPrimaryMainColor),
    },
    secondary: {
      main: "#F6C9A8", // pastel peach
      light: "#fde0c9",
      dark: "#c69576",
      contrastText: contrastColor("#F6C9A8"),
    },
    success: {
      main: "#58855C", // mint
      contrastText: contrastColor("#58855C"),
    },
    info: {
      main: "#4A9DAE", // pastel lavender
      contrastText: contrastColor("#4A9DAE"),
    },
    warning: {
      main: "#8A804C", // soft yellow
      contrastText: contrastColor("#8A804C"),
    },
    error: {
      main: "#FF645C", // soft rose
      contrastText: contrastColor("#FF645C"),
    },
    background: {
      default: "#F7FBFC", // very light pastel-blue/gray
      paper: "#ffffff",
    },
    text: {
      primary: "#0b2540", // deep navy for high readability on light backgrounds
      secondary: "#274050",
      disabled: "rgba(11,37,64,0.38)",
    },
    tonalOffset: 0.2,
  },
  shape: {
    borderRadius: 10,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none",
          borderRadius: 4,
        },
      },
    },
  },
  typography: {
    fontFamily: `Roboto, -apple-system, "Segoe UI", "Helvetica Neue", Arial`,
    h1: { fontWeight: 700 },
    h2: { fontWeight: 600 },
  },
})
