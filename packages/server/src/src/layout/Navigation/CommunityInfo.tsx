import { Box, Typography } from "@mui/material"
import classnames from "classnames"

import { PartyBox } from "../../components/elements/link/PartyBox/PartyBox"
import { COMMUNITY_IMAGES } from "../../config/images"
import { communityRootRoute } from "../../routes/community/community.root.route"
import { COMMUNITY_ROUTE } from "../../routes/paths"

import * as styles from "./navigation.module.css"

export const CommunityInfo = () => {
  const comunityData = communityRootRoute.useLoaderData()

  const id = String(comunityData?.id ?? 0)

  return (
    <Box display="flex" flexDirection="row" gap={2} alignItems="center">
      <Typography variant="h4" className={styles.name}>
        {comunityData?.name ?? ""}
      </Typography>
      <PartyBox
        to={COMMUNITY_ROUTE}
        params={{ communityId: id }}
        title={comunityData?.name ?? ""}
        className={classnames(styles.smallLogo)}
        sx={{
          backgroundImage: `url(${ENV_IMAGE_CDN}${COMMUNITY_IMAGES}/small_${comunityData?.image ?? ""})`,
        }}
      />
    </Box>
  )
}
