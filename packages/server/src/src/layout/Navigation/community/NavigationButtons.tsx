import { Box } from "@mui/material"
import classnames from "classnames"

import { <PERSON>ginButton } from "../../../components/common/LoginButton/LoginButton"
import { PartyBox } from "../../../components/elements/link/PartyBox/PartyBox"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { COMMUNITY_IMAGES } from "../../../config/images"
import { hasPermission } from "../../../permissions"
import { communityRootRoute } from "../../../routes/community/community.root.route"
import {
  COMMUNITY_EDIT_ROUTE,
  COMMUNITY_EVENTS_ROUTE,
  COMMUNITY_ROUTE,
  COMMUNITY_USERS_ROUTE,
  COMMUNITY_USER_ROUTE,
  GAMES_ROUTE,
  INDEX_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import * as styles from "../navigation.module.css"

export const NavigationButtons = () => {
  const isLoggedIn = useUserStore((state) => state.isLoggedIn)
  const userInfo = useUserStore((state) => state.userData)

  const comunityData = communityRootRoute.useLoaderData()

  const id = String(comunityData?.id ?? 0)

  return (
    <>
      <Box justifyContent="space-between" display="flex" gap={2} width="100%">
        <Box display="flex" gap={2} pl={13}>
          {isLoggedIn && (
            <>
              <PartyBox
                to={COMMUNITY_ROUTE}
                params={{ communityId: id }}
                title={comunityData?.name ?? ""}
                className={classnames(styles.logo, styles.nonMobile)}
                sx={{
                  backgroundImage: `url(${ENV_IMAGE_CDN}${COMMUNITY_IMAGES}/small_${comunityData?.image ?? ""})`,
                }}
              />
              <PartyLink
                color="inherit"
                size="large"
                sx={{ fontSize: "1.25rem" }}
                variant="text"
                to={COMMUNITY_ROUTE}
                params={{ communityId: id }}
              >
                {comunityData?.name ?? ""}
              </PartyLink>
              <PartyLink
                color="inherit"
                variant="text"
                to={GAMES_ROUTE}
                params={{ communityId: id }}
                preload="intent"
                preloadDelay={500}
              >
                Games
              </PartyLink>
              <PartyLink
                color="inherit"
                variant="text"
                to={COMMUNITY_EVENTS_ROUTE}
                params={{ communityId: id }}
                preload="intent"
                preloadDelay={500}
              >
                Events
              </PartyLink>
              <PartyLink
                color="inherit"
                variant="text"
                to={COMMUNITY_USERS_ROUTE}
                params={{ communityId: id }}
                preload="intent"
                preloadDelay={500}
              >
                Members
              </PartyLink>
              {hasPermission(userInfo, "community", "update", {
                id: Number(id),
              }) && (
                <PartyLink
                  color="inherit"
                  variant="text"
                  to={COMMUNITY_EDIT_ROUTE}
                  params={{ communityId: id }}
                  preload="intent"
                  preloadDelay={500}
                >
                  Administrate
                </PartyLink>
              )}
              <PartyLink
                to={COMMUNITY_USER_ROUTE}
                params={{
                  communityId: id,
                  userId: String(userInfo?.id ?? 0),
                }}
                color="inherit"
                variant="text"
              >
                Profile
              </PartyLink>
            </>
          )}
        </Box>

        <Box display="flex" gap={2}>
          <PartyLink to={INDEX_ROUTE} color="inherit" variant="text">
            Home
          </PartyLink>
          <LoginButton />
        </Box>
      </Box>
    </>
  )
}
