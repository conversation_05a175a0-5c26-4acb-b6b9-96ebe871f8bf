import { Box, Divider, List } from "@mui/material"

import { LoginButton } from "../../../components/common/LoginButton/LoginButton"
import { Logo } from "../../../components/elements/Logo/Logo"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import {
  COMMUNITY_OPEN_ROUTE,
  EVENTS_ROUTE,
  INDEX_ROUTE,
  PROFILE_ROUTE,
  PUBLIC_COMMUNITIES_ROUTE,
  TAG_LIST_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { NavigationItem } from "../components/NavigationItem"
import * as styles from "../navigation.module.css"

interface DrawerButtonsProps {
  toggleDrawer: () => void
}
export const DrawerButtons = ({ toggleDrawer }: DrawerButtonsProps) => {
  const isLoggedIn = useUserStore((state) => state.isLoggedIn)
  const onClose = () => {
    toggleDrawer()
  }

  return (
    <Box
      onClick={onClose}
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      alignItems="center"
      gap={2}
      height="100%"
      pb={8}
    >
      {isLoggedIn && (
        <>
          <List className={styles.list}>
            <Logo />
            <NavigationItem text="Home" route={INDEX_ROUTE} />
            <PartyLink
              color="inherit"
              variant="text"
              to={EVENTS_ROUTE}
              preload="intent"
              preloadDelay={500}
            >
              Events
            </PartyLink>
            <NavigationItem
              text="Public Communities"
              route={PUBLIC_COMMUNITIES_ROUTE}
            />
            <NavigationItem
              text="My Communities"
              route={COMMUNITY_OPEN_ROUTE}
            />
            <NavigationItem text="Profile" route={PROFILE_ROUTE} />
            <NavigationItem text="Tags" route={TAG_LIST_ROUTE} />
          </List>
          <Divider sx={{ width: "100%" }} />
        </>
      )}
      <LoginButton />
    </Box>
  )
}
