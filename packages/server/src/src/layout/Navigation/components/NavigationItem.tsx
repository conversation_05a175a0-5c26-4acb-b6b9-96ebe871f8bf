import { ListItem, ListItemIcon, ListItemText } from "@mui/material"

import { PartyListItemButton } from "../../../components/elements/link/PartyListItemButton/PartyListItemButton"
import styles from "../navigation.module.css"

import type { LinkComponentProps } from "@tanstack/react-router"

interface NavigationItemProps {
  text: string
  icon?: React.ReactNode
  route: LinkComponentProps["to"]
  params?: LinkComponentProps["params"]
  isMain?: boolean
}
export const NavigationItem = ({
  text,
  icon,
  route,
  params,
  isMain = false,
}: NavigationItemProps) => {
  return (
    <ListItem key={text} disablePadding>
      <PartyListItemButton
        params={params}
        to={route}
        preload="intent"
        preloadDelay={500}
        className={isMain ? styles.listItemMain : styles.listItem}
      >
        {icon && <ListItemIcon>{icon}</ListItemIcon>}
        <ListItemText primary={text} />
      </PartyListItemButton>
    </ListItem>
  )
}
