import { create } from "zustand/index"

interface Threshold {
  key: TSize
  size: number
}

const sizeThreshold: Threshold[] = [
  { key: "mobile", size: 0 },
  { key: "tablet", size: 576 },
  { key: "largeTablet", size: 768 },
  { key: "smallDesktop", size: 1024 },
  { key: "desktop", size: 1280 },
  { key: "largeDesktop", size: 1440 },
]

export type TSize =
  | "mobile"
  | "tablet"
  | "largeTablet"
  | "smallDesktop"
  | "desktop"
  | "largeDesktop"
export interface UseUserStoreProps {
  isMobile: boolean
  size?: TSize
  setIsMobile: (isMobile: boolean) => void
  setCurrentWidth: (currentSize: number) => void
  getThreshold: (size: TSize, sameOrLarger?: boolean) => boolean
  sizeThresholdList: Record<TSize, boolean>
}

export const useIsMobileStore = create<UseUserStoreProps>((set, get) => {
  return {
    isMobile: false,
    size: "desktop",
    sizeThresholdList: {
      mobile: true,
      tablet: false,
      largeTablet: false,
      smallDesktop: false,
      desktop: false,
      largeDesktop: false,
    },
    setIsMobile: (isMobile: boolean) => set(() => ({ isMobile })),
    getThreshold: (size: TSize, sameOrLarger: boolean = true) => {
      const position = getThreshold(size, get().size)
      return sameOrLarger ? position : !position
    },
    setCurrentWidth: (currentSize: number) => {
      const size = sizeThreshold
        .toReversed()
        .find((s: Threshold) => s.size < currentSize)

      return set(() => ({
        size: size?.key,
        sizeThresholdList: {
          mobile: getThreshold("mobile", size?.key),
          tablet: getThreshold("tablet", size?.key),
          largeTablet: getThreshold("largeTablet", size?.key),
          smallDesktop: getThreshold("smallDesktop", size?.key),
          desktop: getThreshold("desktop", size?.key),
          largeDesktop: getThreshold("largeDesktop", size?.key),
        },
      }))
    },
  }
})

const getThreshold = (thresholdSize: TSize, size: TSize | undefined) => {
  const mySize = sizeThreshold.findIndex(
    (s: Threshold) => s.key === thresholdSize,
  )
  const currentSizeName = size

  const actualSize = sizeThreshold.findIndex(
    (s: Threshold) => s.key === currentSizeName,
  )

  return !(actualSize >= mySize)
}
