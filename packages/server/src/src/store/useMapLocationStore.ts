import { create } from "zustand"

import { ICEvent } from "../types/tRPC.types"

export interface UseMapLocationStoreProps {
  location: Pick<ICEvent, "lat" | "lng" | "location"> | null
  locationChange: (lat: number, lng: number, location: string) => void
}

export const useMapLocationStore = create<UseMapLocationStoreProps>((set) => ({
  location: null,
  locationChange: (lat: number, lng: number, location: string) =>
    set(() => ({ location: { lat, lng, location } })),
}))
