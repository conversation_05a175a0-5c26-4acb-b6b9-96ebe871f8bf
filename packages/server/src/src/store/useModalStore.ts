import { create } from "zustand"

export interface UseModalStoreProps {
  modalList: Record<string, { id: string; isOpen: boolean }>
  createModal: (name: string) => void
  openModal: (name: string) => void
  closeModal: (name: string) => void
}

export const useModalStore = create<UseModalStoreProps>((set) => ({
  modalList: {},
  createModal: (name: string) =>
    set((state) => ({
      modalList: state.modalList[name]
        ? state.modalList
        : { ...state.modalList, [name]: { id: name, isOpen: false } },
    })),
  openModal: (name: string) =>
    set((state) => ({
      modalList: { ...state.modalList, [name]: { id: name, isOpen: true } },
    })),
  closeModal: (name: string) =>
    set((state) => ({
      modalList: { ...state.modalList, [name]: { id: name, isOpen: false } },
    })),
}))
