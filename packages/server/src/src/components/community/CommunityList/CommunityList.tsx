import { Box } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { type FC, useCallback } from "react"

import { COMMUNITY_ROUTE } from "../../../routes/paths"
import { TitleRow } from "../../common/TitleRow/TitleRow"
import {
  CommunityThumbnail,
  type ICCommunity,
} from "../CommunityThumbnail/CommunityThumbnail"
import { CreateCommunity } from "../CreateCommunity/CreateCommunity"

import * as styles from "./communityList.module.css"

interface CommunityListProps {
  communities: ICCommunity[]
  title: string
}
export const CommunityList: FC<CommunityListProps> = ({
  communities,
  title,
}) => {
  const navigate = useNavigate()

  const onClick = useCallback(
    (id: number) => {
      navigate({ to: COMMUNITY_ROUTE, params: { communityId: id.toString() } })
    },
    [navigate],
  )

  return (
    <>
      <TitleRow title={title}>
        <CreateCommunity />
      </TitleRow>
      <Box className={styles.container}>
        <Box className={styles.list}>
          {communities.map((community) => (
            <CommunityThumbnail
              key={community.id}
              community={community}
              onClick={onClick}
            />
          ))}
        </Box>
      </Box>
    </>
  )
}
