import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { LoadingButton } from "@mui/lab"
import { Box, Grid2 } from "@mui/material"
import { useState } from "react"
import { FormProvider, type SubmitHandler, useForm } from "react-hook-form"

import {
  type CreateCommunityInputs,
  createCommunityInputs,
} from "../../../schemas"
import { trpc } from "../../../trpc/trpc"
import {
  LoaderDialog,
  type LoaderDialogState,
} from "../../common/LoaderDialog/LoaderDialog"
import { FormInput } from "../../elements/HookElements/FormInput"
import { FormSelect } from "../../elements/HookElements/FormSelect"
import { FormSwitch } from "../../elements/HookElements/FormSwitch"
import { RTEdit } from "../../elements/HookElements/RTEdit/RTEdit"

import type { IBasicCommunity } from "../../../types/tRPC.types"

export interface CreateInfoProps {
  onSuccess: (communityId: number) => void
  edit?: IBasicCommunity
}

export const CommunityConfiguration = ({
  onSuccess,
  edit,
}: CreateInfoProps) => {
  const [savingData, setSavingData] = useState<LoaderDialogState>(null)
  const methods = useForm<CreateCommunityInputs>({
    resolver: zodResolver(createCommunityInputs),
    defaultValues: {
      name: edit?.name ?? "",
      openness: edit?.openness ?? "public",
      online: edit?.online ?? "",
      approval: edit?.approval ?? true,
      location: edit?.location ?? "",
      allowShare: edit?.share ?? true,
      description: edit?.description ?? "",
      welcome: edit?.welcome ?? "",
      events: edit?.events ?? "moders",
    },
  })

  const onSubmit: SubmitHandler<CreateCommunityInputs> = async (data) => {
    setSavingData("loading")
    const mutation = edit
      ? trpc.configureCommunity.mutate({
          ...data,
          id: edit.id ?? 0,
        })
      : trpc.createCommunity.mutate({
          ...data,
        })

    mutation
      .then((response) => {
        if (response.success) {
          onSuccess(response.communityId)
        }

        setSavingData(response.success ? null : "failed")
      })
      .catch((error) => {
        console.error(error)
        setSavingData("failed")
      })
  }

  return (
    <Box padding={4}>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <Grid2 container columns={1} spacing={2} marginTop={2}>
            <Grid2 size={1}>
              <FormInput
                label="Name"
                name="name"
                required={true}
                placeholder="John & His Friends"
              />
            </Grid2>
            <Grid2 size={1}>
              <FormSelect
                label="How public is your community?"
                name="openness"
                helper="* Public - Anyone can see, anyone can join; <br /> * Public Limited - Anyone can see, join on invite; <br /> * Private - Only Members can see, join on invite; <br /> * Closed - Only members can se, nobody can join"
                items={[
                  { title: "Public", value: "public" },
                  { title: "Public Limited", value: "publicLimited" },
                  { title: "Private", value: "private" },
                  { title: "Closed", value: "closed" },
                ]}
              />
            </Grid2>
            <Grid2 size={1}>
              <FormSelect
                label="Who can create new events?"
                name="events"
                items={[
                  { title: "Moders", value: "moders" },
                  { title: "All", value: "all" },
                  { title: "Trusted", value: "trusted" },
                  { title: "None", value: "none" },
                ]}
              />
            </Grid2>
            <Grid2 size={1}>
              <FormInput
                label="Physical location"
                name="location"
                helper="Can add if You community is location based."
                placeholder="Latvia, Riga"
              />
            </Grid2>
            <Grid2 size={1}>
              <FormInput
                label="Online location (link)"
                name="online"
                helper="Can add if You community is reachable online."
                placeholder="https://boardgamegeek.com/"
              />
            </Grid2>
            <Grid2 size={1}>
              <FormSwitch
                label="Manual member approval"
                name="approval"
                helper="You will need to approve new members manually if this is
                        enabled. Otherwise anyone who is eligible can join (eg -
                        for public anyone, for others - anyone who has invite)."
              />
            </Grid2>
            <Grid2 size={1}>
              <FormSwitch
                label="Allow users to share games"
                name="allowShare"
                helper="If this is disabled - nobody will be able to share their
                        collection. You will need to go and do it manually for
                        each member. Recommended for public communities with
                        lot's of members."
              />
            </Grid2>
            <Grid2 size={1}>
              <FormInput
                label="Descrition"
                name="description"
                multiline={true}
              />
            </Grid2>
            <Grid2>
              <RTEdit label="Welcome message" name="welcome" required={false} />
            </Grid2>
            <Grid2 size={1}>
              <LoadingButton
                variant="contained"
                color="primary"
                type="submit"
                loading={savingData === "loading"}
              >
                Save
              </LoadingButton>
            </Grid2>
          </Grid2>
        </form>
      </FormProvider>
      <LoaderDialog
        state={savingData}
        title="Upload Community Logo"
        onClose={() => {
          setSavingData(null)
        }}
      />
    </Box>
  )
}
