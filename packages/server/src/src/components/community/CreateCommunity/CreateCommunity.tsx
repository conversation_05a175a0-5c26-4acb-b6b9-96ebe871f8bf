import { Box, Button } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"

import { hasPermission } from "../../../permissions"
import { CREATE_COMMUNITY_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"

export const CreateCommunity = () => {
  const navigate = useNavigate()
  const user = useUserStore((state) => state.userData)

  return (
    hasPermission(user, "global", "createCommunity", {
      createdCommunities: user.countCommunities,
    }) && (
      <Box>
        <Button
          variant="contained"
          onClick={() => navigate({ to: CREATE_COMMUNITY_ROUTE })}
        >
          Create Community
        </Button>
      </Box>
    )
  )
}
