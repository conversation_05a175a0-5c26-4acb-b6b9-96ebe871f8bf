import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { LoadingButton } from "@mui/lab"
import { Box, FormControl, Grid2, Typography } from "@mui/material"
import TextField from "@mui/material/TextField"
import { type ChangeEvent, useState } from "react"
import { Controller, type SubmitHandler, useForm } from "react-hook-form"

import { COMMUNITY_IMAGES } from "../../../config/images"
import { useFirebase } from "../../../hooks/useFirebase"
import { COMMUNITY_ROUTE } from "../../../routes/paths"
import { type UploadImageInput, uploadImageInput } from "../../../schemas"
import {
  LoaderDialog,
  type LoaderDialogState,
} from "../../common/LoaderDialog/LoaderDialog"
import { PartyLink } from "../../elements/link/PartyLink/PartyLink"

import * as styles from "./communityUploadImage.module.css"

export interface CommunityUploadImageProps {
  onSuccess: () => void
  hasImage?: string
  id: number | null
  notNew?: boolean
}

export const CommunityUploadImage = ({
  id,
  onSuccess,
  hasImage,
  notNew,
}: CommunityUploadImageProps) => {
  const { isAuthenticated, getAccessTokenSilently } = useFirebase()
  const [savingData, setSavingData] = useState<LoaderDialogState>(null)

  const { handleSubmit: handleSubmitUpload, control: controlUpload } =
    useForm<UploadImageInput>({
      resolver: zodResolver(uploadImageInput),
      defaultValues: {
        image: "",
      },
    })
  const onSubmitUpload: SubmitHandler<UploadImageInput> = async (data) => {
    // do the upload
    const formData = new FormData()
    formData.append("image", data.image)
    formData.append("communityId", String(id))

    setSavingData("loading")
    if (isAuthenticated) {
      fetch(`${ENV_API_URL}/changeCommunity`, {
        method: "POST",
        body: formData,
        headers: {
          "Access-Control-Allow-Origin": "*",
          Authorization: `Bearer ${await getAccessTokenSilently()}`,
        },
      })
        .then((response) => response.text())
        .then((text) => {
          if (text === "OK") {
            onSuccess()
          }

          setSavingData(null)
        })
        .catch((error) => {
          console.error(error)
          setSavingData("failed")
        })
    }
  }

  return (
    <Box className={styles.upload}>
      {hasImage && (
        <Box>
          <img
            src={`${ENV_IMAGE_CDN}${COMMUNITY_IMAGES}/large_${hasImage}`}
            alt="Community logo"
            className={styles.image}
          />
        </Box>
      )}
      <form onSubmit={handleSubmitUpload(onSubmitUpload)}>
        <Grid2 container columns={1} spacing={2}>
          {!hasImage && (
            <Grid2>
              <Typography variant="subtitle1">
                Please upload logo. Otherwise default image will be displayed.
              </Typography>
            </Grid2>
          )}
          <Grid2 size={1}>
            <Controller
              name="image"
              control={controlUpload}
              render={({ field: { onChange }, fieldState: { error } }) => (
                <FormControl error={!!error}>
                  <TextField
                    helperText={error ? error.message : null}
                    type="file"
                    error={!!error}
                    onChange={(event: ChangeEvent<HTMLInputElement>) => {
                      onChange(event.target.files)
                    }}
                    name="image"
                    fullWidth
                  />
                </FormControl>
              )}
            />
          </Grid2>
          <Grid2 size={1}>
            <Box className={styles.buttons}>
              <LoadingButton
                variant="contained"
                color="primary"
                type="submit"
                loading={savingData === "loading"}
              >
                Upload
              </LoadingButton>
              {!notNew && (
                <PartyLink
                  variant="contained"
                  color="primary"
                  to={COMMUNITY_ROUTE}
                  params={{ communityId: String(id) }}
                >
                  Skip
                </PartyLink>
              )}
            </Box>
          </Grid2>
        </Grid2>
      </form>
      <LoaderDialog
        state={savingData}
        title="Upload Community Logo"
        onClose={() => {
          setSavingData(null)
        }}
      />
    </Box>
  )
}
