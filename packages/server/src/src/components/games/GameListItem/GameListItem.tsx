import NewReleasesIcon from "@mui/icons-material/NewReleases"
import { Box, Typography } from "@mui/material"
import { type LinkComponentProps } from "@tanstack/react-router"
import classnames from "classnames"
import dayjs from "dayjs"
import localizedFormat from "dayjs/plugin/localizedFormat"

import { GAME_IMAGES } from "../../../config/images"
import { calculateRed } from "../../../utils/colorCalculation"
import { PartyBox } from "../../elements/link/PartyBox/PartyBox"
import {
  type ICAvatarUser,
  type ICFAvatarOnClick,
} from "../../user/UserAvatar/UserAvatar"
import { UserAvatarGroup } from "../../user/UserAvatarGroup/UserAvatarGroup"
import { BggLink } from "../BggLink/BggLink"

import * as styles from "./gameListItem.module.css"

export interface ICThumbnailGame {
  id: number
  title: string
  bggId: number
  rating?: number | null
  average?: number | null
  playCount?: number | null
  lastPlay?: string | null
}

export type ICNavigationProps = Pick<
  LinkComponentProps,
  "to" | "search" | "params"
>

interface GameListItemProps {
  game: ICThumbnailGame
  communityId?: number
  onUser?: ICFAvatarOnClick
  userList?: ICAvatarUser[]
  isNew?: boolean
  displayOpen?: boolean
  className?: string
  elevation?: number
  navigation?: ICNavigationProps
  isExpansion?: boolean
  isBase?: boolean
  onClick?: (id: number) => void
}
export const GameListItem = ({
  game,
  onUser,
  userList,
  isNew = false,
  className = "",
  navigation,
  onClick,
}: GameListItemProps) => {
  dayjs.extend(localizedFormat)
  return (
    <PartyBox
      to={navigation?.to}
      search={navigation?.search}
      params={navigation?.params}
      key={game.id}
      className={classnames(styles.card, className, {
        [styles.pointer]: !!navigation,
      })}
      title={game.title}
      preload="intent"
      preloadDelay={1000}
      onClick={() => onClick?.(game.id)}
    >
      <Box className={styles.imageContainer}>
        <img
          className={styles.image}
          src={`${ENV_IMAGE_CDN}${GAME_IMAGES}/${game.id}.jpg`}
          loading="lazy"
          alt={game.title}
        />
        {isNew && (
          <Box className={styles.newGame} title="New">
            <NewReleasesIcon color="primary" fontSize="large" />
          </Box>
        )}
        <Box className={styles.bggLink}>
          <BggLink bggId={game.bggId} />
          {game.average && (
            <Box className={styles.bggRating}>
              <Typography fontWeight={600}>
                {Math.round(game.average * 10) / 10}
              </Typography>
            </Box>
          )}
        </Box>
        {game.rating && game.rating > 0 && (
          <Box
            className={styles.userRating}
            title={`User rating: ${game.rating}`}
          >
            <Typography
              variant="body2"
              fontWeight={500}
              sx={{ color: calculateRed(Math.round(game.rating)) }}
            >
              {game.rating}
            </Typography>
          </Box>
        )}
      </Box>
      <Box display="flex" flexDirection="column" gap={0.5}>
        <Box>
          <Typography
            variant="body2"
            fontWeight={600}
            className={styles.title}
            title={game.title}
          >
            {game.title}
          </Typography>
        </Box>
        {(game.playCount ?? 0) > 0 && (
          <>
            <Box title={`Total plays: ${game.playCount}`}>
              <Typography fontWeight={500}>
                Total plays: {game.playCount}
              </Typography>
            </Box>
            <Box title={`Last played: ${dayjs(game.lastPlay).format("ll")}`}>
              <Typography fontWeight={500}>
                Played: {dayjs(game.lastPlay).format("l")}
              </Typography>
            </Box>
          </>
        )}

        <Box display="flex" justifyContent="flex-start">
          {userList && <UserAvatarGroup users={userList} onClick={onUser} />}
        </Box>
      </Box>
    </PartyBox>
  )
}
