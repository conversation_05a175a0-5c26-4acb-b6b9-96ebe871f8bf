.card {
  width: 100%;
  height: 104px;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  border-bottom: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: var(--spacing-2);
}

.card:first-child {
  border-top: 1px solid #ccc;
}

.title {
  line-clamp: 2;
  overflow: hidden;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.pointer {
  cursor: pointer;
}

.imageContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.image {
  width: 100px;
  height: 100px;
  object-fit: contain;
}

.newGame {
  position: absolute;
  top: 0;
  left: 0;
}

.bggLink {
  position: absolute;
  bottom: 0;
  left: 0;
}

.bggRating {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.bubbles {
  position: absolute;
  cursor: default;
  background-color: #fff;
  border: 1px solid;
  top: 8px;
  left: 8px;
  vertical-align: middle;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.userRating {
  composes: bubbles;
  width: 26px;
  height: 26px;
  top: 0;
  right: 0;
  left: auto;
}
