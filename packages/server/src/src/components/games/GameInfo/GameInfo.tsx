import { Box, Typography } from "@mui/material"
import { useCallback } from "react"

import { GAME_IMAGES } from "../../../config/images"
import { BggLink } from "../BggLink/BggLink"
import { GameInfoRow } from "../GameInfoRow/GameInfoRow"

import * as styles from "./gameInfo.module.css"

import type {
  IGameGame,
  IItemInfo,
  IUserProfileGameDetails,
} from "../../../types/tRPC.types"

type Item = IGameGame | IUserProfileGameDetails | IItemInfo["item"]
function isItemInfo(pet: Item): pet is IItemInfo["item"] {
  return (pet as IItemInfo["item"]).description !== undefined
}

interface GameInfoProps {
  game: Item
  full?: boolean
}
export const GameInfo = ({ game, full = false }: GameInfoProps) => {
  const getPlayerCount = useCallback(() => {
    const recommended: string[] = []
    const best: string[] = []
    game.players.stats?.forEach((count) => {
      const title = !count[2] ? String(count[0]) : `${count[0]}+`
      if (count[1] === 1) {
        best.push(title)
      }
      if (count[1] === 2) {
        recommended.push(title)
      }
    })
    return (
      <Box display="flex" flexDirection="column">
        <Box>{`Best: ${best.join(", ")}`}</Box>
        <Box>{`Rec.: ${recommended.join(", ")}`}</Box>
      </Box>
    )
  }, [game.players.stats])

  const displayFullItem = isItemInfo(game) && full

  return (
    <>
      <Box className={styles.infoRowCell}>
        {displayFullItem && (
          <Box className={styles.imageContainer}>
            <img
              src={`${ENV_IMAGE_CDN}${GAME_IMAGES}/${game.id}.jpg`}
              loading="lazy"
              alt={game.title}
            />
          </Box>
        )}
        <Box className={styles.gridContainer}>
          <GameInfoRow title="Title:" value={game.title} />
          <GameInfoRow
            title="Box player count:"
            tooltip="The player count as listed on the game box"
            value={
              game.players.box.min === game.players.box.max
                ? String(game.players.box.min)
                : `${game.players.box.min} - ${game.players.box.max}`
            }
          />
          <GameInfoRow
            title="BGG player count:"
            value={getPlayerCount()}
            tooltip="The player count as recommended by the BGG community"
          />
          <GameInfoRow
            title="Box play length:"
            tooltip="The play length as listed on the game box"
            value={
              game.length.box.min === game.length.box.max
                ? String(game.length.box.min)
                : `${game.length.box.min} - ${game.length.box.max}`
            }
          />
          <GameInfoRow title="Age:" value={`${game.age}+`} />
          <GameInfoRow
            title="BGG link:"
            value={<BggLink bggId={game.bggId} text />}
          />
          {displayFullItem && (
            <>
              <GameInfoRow
                title="Average score:"
                value={game.average}
                tooltip="The average rating of the game on BGG"
              />
              {game.weight && (
                <GameInfoRow
                  title="Weight:"
                  value={game.weight}
                  tooltip="The weight of the game on BGG"
                />
              )}
              <GameInfoRow
                title="Year published:"
                value={game.publishYear}
                tooltip="The year the game was published"
              />
              <GameInfoRow
                title="Rank:"
                value={game.rank}
                tooltip="The rank of the game on BGG"
              />
            </>
          )}
        </Box>
        {displayFullItem && game.description && (
          <Box p={2}>
            <Typography
              variant="subtitle1"
              color="textPrimary"
              fontWeight={600}
            >
              Description:
            </Typography>
            <Typography variant="subtitle1" color="textSecondary">
              {game.description}
            </Typography>
          </Box>
        )}
      </Box>
    </>
  )
}
