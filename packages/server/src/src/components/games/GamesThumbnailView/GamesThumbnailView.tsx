import { Box, Pagination } from "@mui/material"
import { useCallback, useMemo } from "react"

import { GAMES_PER_PAGE } from "../../../config/game.conf"
import { type ICNavigationProps } from "../GameThumbnail/GameThumbnail"

import {
  GameThumbnailWrapper,
  type ICUserThumbnailWrapperGame,
} from "./GameThumbnailWrapper"

interface GamesListProps {
  games: ICUserThumbnailWrapperGame[]
  onUser?: (gameId: number) => void
  onPageChange: (page: number) => void
  page?: number
  communityId?: number
  navigation?: ICNavigationProps
  listMode?: boolean
}
export const GamesThumbnailView = ({
  games,
  page,
  onUser,
  communityId,
  onPageChange,
  navigation = {},
  listMode = false,
}: GamesListProps) => {
  const onChange = useCallback(
    (_: React.ChangeEvent<unknown>, page: number) => {
      onPageChange(page)
    },
    [onPageChange],
  )

  const pages = Math.ceil(games.length / GAMES_PER_PAGE)
  const startItem = GAMES_PER_PAGE * ((page ?? 1) - 1)
  const endItem = startItem + GAMES_PER_PAGE

  const navParams = navigation?.params !== true ? navigation?.params : {}

  const pagination = useMemo(
    () =>
      pages > 1 ? (
        <Box
          display="flex"
          justifyContent="center"
          flexDirection="row"
          alignItems="center"
        >
          <Pagination
            shape="rounded"
            count={pages}
            page={page}
            variant="outlined"
            onChange={onChange}
            sx={{
              "& .MuiPagination-ul": {
                flexWrap: "nowrap",
              },
            }}
          />
        </Box>
      ) : null,
    [page, pages, onChange],
  )

  return (
    <Box
      display="flex"
      justifyContent="center"
      flexDirection="column"
      gap={4}
      pt={2}
      pb={8}
    >
      {pagination}
      <Box
        display="flex"
        gap={listMode ? 0 : 2}
        flexWrap="wrap"
        justifyContent="center"
      >
        {games.slice(startItem, endItem).map((game) => (
          <GameThumbnailWrapper
            listMode={listMode}
            navigation={{
              ...navigation,
              params: { ...navParams, gameId: game.id.toString() },
            }}
            onUser={onUser}
            game={game}
            communityId={communityId}
            key={game.id}
          />
        ))}
      </Box>
      {pagination}
    </Box>
  )
}
