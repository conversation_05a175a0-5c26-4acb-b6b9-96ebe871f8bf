import { useMemo } from "react"

import { useGameStore } from "../../../store/useGamesStore"
import { isNewEntry } from "../../../utils/transformTime"
import { type ICFAvatarOnClick } from "../../user/UserAvatar/UserAvatar"
import { GameListItem } from "../GameListItem/GameListItem"
import {
  GameThumbnail,
  type ICNavigationProps,
  type ICThumbnailGame,
} from "../GameThumbnail/GameThumbnail"

export type ICUserThumbnailWrapperGame = ICThumbnailGame & {
  news?: string | null
  users?: [number, number][]
}

interface GameThumbnailProps {
  game: ICUserThumbnailWrapperGame
  communityId?: number
  onUser?: ICFAvatarOnClick
  navigation?: ICNavigationProps
  listMode?: boolean
}
export const GameThumbnailWrapper = ({
  game,
  communityId,
  onUser,
  navigation,
  listMode = false,
}: GameThumbnailProps) => {
  const { getPopulatedUser } = useGameStore()
  const isNew = game.news ? !isNewEntry(game.news) : false

  const userList = useMemo(() => {
    return game.users
      ? game.users
          .sort((a, b) => (a[1] > b[1] ? -1 : 1))
          .map(
            (user: [number, number]) => getPopulatedUser(user[0]) ?? undefined,
          )
          .filter((user) => user !== undefined)
      : undefined
  }, [game.users, getPopulatedUser])

  const Component = listMode ? GameListItem : GameThumbnail

  return (
    <Component
      navigation={navigation}
      game={game}
      communityId={communityId}
      onUser={onUser}
      userList={userList}
      isNew={isNew}
    />
  )
}
