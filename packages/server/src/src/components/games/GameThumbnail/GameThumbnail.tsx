import CalendarMonthIcon from "@mui/icons-material/CalendarMonth"
import NewReleasesIcon from "@mui/icons-material/NewReleases"
import { Box, Card, CardContent, Typography } from "@mui/material"
import { type LinkComponentProps } from "@tanstack/react-router"
import classnames from "classnames"

import { GAME_IMAGES } from "../../../config/images"
import { calculateRed } from "../../../utils/colorCalculation"
import { utcToLocal } from "../../../utils/transformTime"
import { PartyLink } from "../../elements/link/PartyLink/PartyLink"
import {
  type ICAvatarUser,
  type ICFAvatarOnClick,
} from "../../user/UserAvatar/UserAvatar"
import { UserAvatarGroup } from "../../user/UserAvatarGroup/UserAvatarGroup"
import { BggLink } from "../BggLink/BggLink"

import * as styles from "./gameThumbnail.module.css"

export interface ICThumbnailGame {
  id: number
  title: string
  bggId: number
  rating?: number | null
  average?: number | null
  playCount?: number | null
  lastPlay?: string | null
}

export type ICNavigationProps = Pick<
  LinkComponentProps,
  "to" | "search" | "params"
>

interface GameThumbnailProps {
  game: ICThumbnailGame
  communityId?: number
  onUser?: ICFAvatarOnClick
  userList?: ICAvatarUser[]
  isNew?: boolean
  displayOpen?: boolean
  className?: string
  elevation?: number
  navigation?: ICNavigationProps
  isExpansion?: boolean
  isBase?: boolean
  onClick?: (id: number) => void
}
export const GameThumbnail = ({
  game,
  onUser,
  userList,
  isNew = false,
  className = "",
  elevation = 1,
  navigation,
  isExpansion = false,
  isBase = false,
  onClick,
}: GameThumbnailProps) => {
  return (
    <Box className={styles.container}>
      <PartyLink
        className={classnames(styles.cardContainer, className, {
          [styles.pointer]: !!navigation,
          [styles.base]: isBase,
        })}
        to={navigation?.to}
        search={navigation?.search}
        params={navigation?.params}
        key={game.id}
        title={game.title}
        preload="intent"
        aria-label={`Open ${game.title}`}
        preloadDelay={1000}
        onClick={() => onClick?.(game.id)}
      >
        <Card elevation={elevation} className={styles.card}>
          <Box className={styles.imageContainer}>
            <img
              className={styles.image}
              src={`${ENV_IMAGE_CDN}${GAME_IMAGES}/${game.id}.jpg`}
              loading="lazy"
              alt={`Box art for ${game.title}`}
            />
          </Box>
          <CardContent className={styles.cardContent}>
            <Box>
              <Typography>{game.title}</Typography>
            </Box>
          </CardContent>
          {isNew && (
            <Box className={styles.newGame} title="New">
              <NewReleasesIcon
                color="primary"
                fontSize="large"
                aria-label="This game is new in community"
              />
            </Box>
          )}
          {game.rating && game.rating > 0 && (
            <Box
              className={styles.userRating}
              title={`User rating: ${game.rating}`}
              aria-label="User rating"
            >
              <Typography
                variant="h6"
                fontWeight={500}
                sx={{ color: calculateRed(Math.round(game.rating)) }}
              >
                {game.rating}
              </Typography>
            </Box>
          )}
          {(game.playCount ?? 0) > 0 && (
            <Box
              className={styles.playCount}
              title={`Total plays: ${game.playCount}`}
              aria-label="Total plays"
            >
              <Typography fontWeight={500}>{game.playCount}</Typography>
            </Box>
          )}
          {game.lastPlay && (
            <Box
              className={styles.lastPlay}
              title={`Last played: ${utcToLocal(game.lastPlay)}`}
              aria-label={`Last played: ${utcToLocal(game.lastPlay)}`}
            >
              <CalendarMonthIcon />
            </Box>
          )}
          {isBase && (
            <Typography
              variant="body2"
              color="textSecondary"
              className={styles.baseInfo}
              title="Base"
              aria-label={`This is base game`}
            >
              Base
            </Typography>
          )}
          {isExpansion && (
            <Typography
              variant="body2"
              color="textSecondary"
              className={styles.baseInfo}
              title="Base"
              aria-label={`This is expansion for the game`}
            >
              Expansion
            </Typography>
          )}
        </Card>
      </PartyLink>
      <Box className={styles.overlayContainer}>
        <Box className={styles.bggLink}>
          <BggLink
            bggId={game.bggId}
            aria-label={`Open ${game.title} on BGG`}
          />
          {game.average && (
            <Box className={styles.bggRating}>
              <Typography fontWeight={600} aria-label="BGG rating">
                {Math.round(game.average * 10) / 10}
              </Typography>
            </Box>
          )}
        </Box>
        <Box
          display="flex"
          justifyContent="flex-start"
          className={styles.userList}
        >
          {userList && <UserAvatarGroup users={userList} onClick={onUser} />}
        </Box>
      </Box>
    </Box>
  )
}
