import { Box, Typography } from "@mui/material"

import { LabelTooltip } from "../../elements/LabelTooltip/LabelTooltip"

import * as styles from "./infoRow.module.css"

interface InfoRowProps {
  title: string
  value: React.ReactNode
  tooltip?: React.ReactNode
}
export const GameInfoRow = ({ title, value, tooltip }: InfoRowProps) => {
  return (
    <>
      <Box>
        <Box className={styles.titleContainer}>
          {tooltip && <LabelTooltip>{tooltip}</LabelTooltip>}
          <Typography
            component="span"
            variant="body1"
            fontWeight={600}
            textAlign="right"
            className={styles.title}
          >
            {title}
          </Typography>
        </Box>
      </Box>
      <Box>
        <Box display="flex" justifyContent="flex-start" alignItems="center">
          <Typography variant="body1" lineHeight={1.5} component="span">
            {value}
          </Typography>
        </Box>
      </Box>
    </>
  )
}
