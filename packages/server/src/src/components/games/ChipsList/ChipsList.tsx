import { Box, Chip } from "@mui/material"

import type { IGameViewTag } from "../../../types/tRPC.types"
interface ChipsListProps {
  tagList: IGameViewTag[]
  onOpenTag?: (text: IGameViewTag) => void
}

export const ChipsList = ({ tagList, onOpenTag }: ChipsListProps) => {
  const handleClick = (tag: IGameViewTag) => {
    if (onOpenTag) {
      onOpenTag(tag)
    }
  }

  return (
    <Box
      padding={2}
      gap={0.5}
      display="flex"
      flexDirection="row"
      flexWrap="wrap"
    >
      {tagList.map((tag) => (
        <Chip
          sx={{
            backgroundColor: `#${tag.color}`,
          }}
          title={`${tag.type}: ${tag.title}`}
          key={tag.title}
          component="div"
          label={tag.title}
          variant="outlined"
          onClick={() => {
            handleClick(tag)
          }}
        />
      ))}
    </Box>
  )
}
