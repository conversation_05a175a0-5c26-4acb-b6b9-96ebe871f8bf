import type { ITag } from "../../../types/tRPC.types"

export const fixedTags: Pick<ITag, "title" | "type">[] = [
  {
    title: "Short",
    type: 2,
  },
  {
    title: "Medium",
    type: 2,
  },
  {
    title: "Long",
    type: 2,
  },
  {
    title: "Extra Long",
    type: 2,
  },
  {
    title: "Lightest",
    type: 1,
  },
  {
    title: "Light",
    type: 1,
  },
  {
    title: "Average",
    type: 1,
  },
  {
    title: "Heavy",
    type: 1,
  },
  {
    title: "Heaviest",
    type: 1,
  },
]
