import { Box, Button, ButtonGroup, Typography } from "@mui/material"
import { memo, useEffect } from "react"

import { useIsMobileStore } from "../../../../store/useIsMobileStore"
import { LabelTooltip } from "../../../elements/LabelTooltip/LabelTooltip"
import { NumberField } from "../../../elements/NumberField/NumberField"

import * as styles from "./playerCountFilter.module.css"

interface PlayerCountFilterProps {
  minPlayers?: number
  maxPlayers?: number
  onChangeMin: (min: number) => void
  onChangeMax: (max: number) => void
  onSetPlayerLevel: (value: number) => void
  playerLevel?: number
  onReset: () => void
}

const playerLevelCycle = ["Exact", "Best", "Recommend", "Box"]

export const PlayerCountFilter = memo(
  ({
    minPlayers = 0,
    maxPlayers = 0,
    onChangeMin,
    onChangeMax,
    playerLevel,
    onReset,
    onSetPlayerLevel,
  }: PlayerCountFilterProps) => {
    const currentCountLevel = playerLevel ?? 0
    const sizeThresholdList = useIsMobileStore(
      (state) => state.sizeThresholdList,
    )

    const newCountLevel = currentCountLevel < 3 ? currentCountLevel + 1 : 0
    const handleChangeMin = (value: number) => onChangeMin(value)

    const handleChangeMax = (value: number) => onChangeMax(value)

    useEffect(() => {
      if (minPlayers > maxPlayers && currentCountLevel > 0) {
        onChangeMin(maxPlayers)
      }
    }, [minPlayers, maxPlayers, onChangeMin, currentCountLevel])

    return (
      <Box className={styles.container}>
        <Box className={styles.controlContainer}>
          <NumberField
            label={currentCountLevel > 0 ? "Min" : "Pick"}
            min={0}
            value={minPlayers ?? 0}
            max={currentCountLevel > 0 ? maxPlayers : 100}
            onChangeValue={handleChangeMin}
          />
          {currentCountLevel > 0 && (
            <NumberField
              label="Max"
              min={minPlayers ?? 0}
              value={maxPlayers ?? 0}
              onChangeValue={handleChangeMax}
            />
          )}
        </Box>
        <Box className={styles.buttonContainer}>
          <Box display="flex" flexDirection="row" gap={1}>
            <Typography variant="caption" className={styles.label}>
              Player count
            </Typography>
            <LabelTooltip
              placement={sizeThresholdList.largeTablet ? "top-end" : "right"}
            >
              <Box display="flex" flexDirection="column" gap={1}>
                <Typography variant="body1">
                  {`Current player count filter type: ${playerLevelCycle[currentCountLevel]}`}
                </Typography>
                <Typography variant="body2">
                  {`Exact - BGG Best/Recommend player count, at least one Best or Recommended value matches search value`}
                </Typography>
                <Typography variant="body2">
                  {`Best - BGG Best player count, at least one Best value within search range`}
                </Typography>
                <Typography variant="body2">
                  {`Recommend - BGG Recommended or Best player count, at least one Best or Recommended value within search range`}
                </Typography>
                <Typography variant="body2">
                  {`Box - Game box player count, search range within game box player count`}
                </Typography>
              </Box>
            </LabelTooltip>
          </Box>
          <ButtonGroup title="Player count filter controls">
            <Button
              variant="outlined"
              className={styles.button}
              size={sizeThresholdList.smallDesktop ? "large" : "small"}
              onClick={() => onSetPlayerLevel(newCountLevel)}
            >
              {playerLevelCycle[currentCountLevel]}
            </Button>
            <Button
              variant="outlined"
              className={styles.button}
              size={sizeThresholdList.smallDesktop ? "large" : "small"}
              onClick={onReset}
            >
              Clear
            </Button>
          </ButtonGroup>
        </Box>
      </Box>
    )
  },
)
