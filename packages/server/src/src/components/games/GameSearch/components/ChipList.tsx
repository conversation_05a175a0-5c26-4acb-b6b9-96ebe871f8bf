import { Box, Chip } from "@mui/material"

export interface ICTag {
  title: string
  type: number | null
}

export interface ICTagCat {
  id?: number
  color?: string
  title: string
}

interface ChipListProps {
  tagCategories: ICTagCat[]
  tagList: ICTag[]
  onChip: (chip: string) => void
}
export const ChipList = ({ tagCategories, tagList, onChip }: ChipListProps) => (
  <Box padding={2} gap={0.5} display="flex" flexDirection="row" flexWrap="wrap">
    {tagList.map((tag) => {
      const category = tagCategories.find((cat) => cat.id === tag.type)
      return (
        <Chip
          sx={{
            backgroundColor: `#${category?.color}`,
          }}
          title={`${category?.title}: ${tag.title}`}
          key={tag.title}
          component="div"
          label={tag.title}
          variant="outlined"
          onClick={() => onChip(tag.title!)}
        />
      )
    })}
  </Box>
)
