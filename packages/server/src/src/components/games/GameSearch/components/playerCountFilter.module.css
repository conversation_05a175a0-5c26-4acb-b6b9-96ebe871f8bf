@value isTablet, isLargeTablet, isSmallDesktop from "../../../../css/size.module.css";

.container {
  display: flex;
  gap: var(--spacing-1);
  align-items: center;
  justify-content: center;
}

.button {
  overflow: hidden;
  text-overflow: ellipsis;
  text-wrap: nowrap;
}

.buttonContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.label {
  font-size: 0.7rem;
  text-wrap: nowrap;
  user-select: none;
}

.controlContainer {
  display: flex;
  gap: var(--spacing-1);
  align-items: center;
  justify-content: center;
  flex-direction: row;
}

@media (max-width: isLargeTablet) {
  .controlContainer {
    gap: var(--spacing-2);
    flex-direction: column;
  }

  .container {
    flex-direction: column-reverse;
    gap: var(--spacing-2);
    width: 100%;
  }

  .label {
    font-size: 1rem;
  }
}
