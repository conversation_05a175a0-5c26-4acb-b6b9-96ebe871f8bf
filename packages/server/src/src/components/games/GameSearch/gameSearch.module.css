@value isTablet, isLargeTablet, isSmallDesktop from "../../../css/size.module.css";

.container {
  display: flex;
  padding: var(--spacing-2);
  gap: var(--spacing-2);
  width: 100%;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  box-sizing: border-box;
  max-width: max-content;
  align-items: center;
}

@media (max-width: isLargeTablet) {
  .container {
    flex-direction: column-reverse;
    align-items: flex-start;
    gap: var(--spacing-4);
  }
}

.dialogContent {
  align-content: center;
  justify-content: center;
  max-width: 300px;
  margin: auto;
  gap: var(--spacing-3);
  display: flex;
  flex-direction: column;
}

.moreButton {
  text-wrap-mode: nowrap;
  max-width: none;
}

.containerMobile {
  display: flex;
  padding: var(--spacing-2);
  gap: var(--spacing-2);
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  max-width: 700px;
}
