/// <reference types="vite-plugin-svgr/client" />
import { Typography } from "@mui/material"

import { default as Bgg<PERSON><PERSON> } from "../../../../assets/svg/bggLogo.svg"
import { MUIPrimaryMainColor } from "../../../theme"

import type { JSX } from "react"

export const BggLink = ({
  bggId,
  text = false,
}: {
  bggId: number
  text?: boolean
}): JSX.Element => {
  return (
    <a
      href={`https://boardgamegeek.com/boardgame/${bggId}`}
      target="_blank"
      onFocus={(e) => e.stopPropagation()}
      title="Open on BGG"
      onClick={(e) => e.stopPropagation()}
      style={{ textDecorationColor: MUIPrimaryMainColor }}
    >
      {text ? (
        <Typography color="textPrimary" variant="body1">
          Open on BGG
        </Typography>
      ) : (
        <BggLogo />
      )}
    </a>
  )
}
