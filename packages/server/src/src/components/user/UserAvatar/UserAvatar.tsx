import { Avatar } from "@mui/material"
import classnames from "classnames"
import { useMemo } from "react"

import { PROFILE_IMAGES } from "../../../config/images"
import { getInitials } from "../../../utils/transformText"

import * as styles from "./userAvatar.module.css"

export interface ICAvatarUser {
  name: string
  id: number
  avatar: string | null
  color: string | null
}

export type ICFAvatarOnClick = (user: number) => void
interface UserAvatarProps {
  user: ICAvatarUser
  size?: "small" | "large"
  onClick?: ICFAvatarOnClick
  labelInfo: string
  noFocus?: boolean
}

export const UserAvatar = ({
  user,
  size = "small",
  onClick,
  labelInfo,
  noFocus = false,
}: UserAvatarProps) => {
  const { id, name, avatar, color } = user

  return useMemo(
    () => (
      <Avatar
        alt={name}
        src={
          avatar
            ? `${ENV_IMAGE_CDN}${PROFILE_IMAGES}/${size}_${avatar}`
            : undefined
        }
        title={name}
        tabIndex={noFocus ? -1 : 0}
        sx={{
          backgroundColor: avatar ? "#ffffff" : `#${color}`,
          color: "#000000",
        }}
        aria-label={`${labelInfo} ${name}`}
        className={classnames(styles.avatar, {
          [styles.large]: size === "large",
          [styles.isClickable]: !!onClick,
        })}
        onFocus={(event) => event.stopPropagation()}
        onClick={(event) => {
          event.stopPropagation()
          onClick?.(id)
        }}
        onKeyUp={(event) => {
          if (event.key === "Enter") {
            event.stopPropagation()
            onClick?.(id)
          }
        }}
      >
        {avatar ? "" : getInitials(name)}
      </Avatar>
    ),
    [id, name, avatar, color, onClick],
  )
}
