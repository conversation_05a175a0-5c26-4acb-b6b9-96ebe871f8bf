@value isSmallDesktop, isTablet from "../../../css/size.module.css";

.userRow {
  display: flex;
  flex-direction: row;
  gap: var(--spacing-1);
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  background-color: #eee;
  cursor: pointer;
  justify-content: space-between;
  transition: background-color 0.3s ease;
}

.userRow:hover {
  background-color: #ddd;
}

.selected {
  background-color: #ccc;
}

.container {
  max-width: 300px;
}

@media (max-width: isSmallDesktop) {
  .container {
    max-width: 100%;
  }
}
