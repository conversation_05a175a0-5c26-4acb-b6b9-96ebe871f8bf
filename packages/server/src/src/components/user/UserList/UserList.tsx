import { Box } from "@mui/material"

import { ICUserRow, UserRow } from "../UserRow/UserRow"

type UserListProps = {
  users: ICUserRow[]
  labelInfo?: string
  communityId?: string
  eventId?: number
}
export const UserList = ({
  users,
  labelInfo = "",
  communityId,
  eventId,
}: UserListProps) => {
  return (
    <Box
      alignContent="center"
      justifyContent="center"
      display="flex"
      flexDirection="row"
      gap={4}
      flexWrap="wrap"
      padding={5}
    >
      {users
        .sort((u1, u2) => (u1.name > u2.name ? 1 : -1))
        .map((user) => (
          <UserRow
            user={user}
            key={user.id}
            labelInfo={labelInfo}
            communityId={communityId}
            eventId={eventId}
          />
        ))}
    </Box>
  )
}
