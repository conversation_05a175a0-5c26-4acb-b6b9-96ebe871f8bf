import { AvatarGroup } from "@mui/material"
import { useMemo } from "react"

import {
  type ICAvatarUser,
  type ICFAvatarOnClick,
  UserAvatar,
} from "../UserAvatar/UserAvatar"

interface UserAvatarGroupProps {
  users: ICAvatarUser[]
  onClick?: ICFAvatarOnClick
}

export const UserAvatarGroup = ({ users, onClick }: UserAvatarGroupProps) => {
  if (!users) {
    return null
  }

  return useMemo(
    () => (
      <AvatarGroup max={4}>
        {users.map((user) => (
          <UserAvatar
            labelInfo="Owner: "
            onClick={onClick}
            key={user.id}
            user={user}
          />
        ))}
      </AvatarGroup>
    ),
    [users, onClick],
  )
}
