import AccessTimeIcon from "@mui/icons-material/AccessTime"
import {
  Box,
  Card,
  CardActions,
  CardContent,
  CardMedia,
  Typography,
} from "@mui/material"

import { createImageLink } from "../../../config/images"
import { ICEvent } from "../../../types/tRPC.types"
import { localToLocalWithDay } from "../../../utils/transformTime"
import { EventStatus } from "../EventStatus/EventStatus"

import { EventButtons } from "./components/EventButtons"
import { HostInfo } from "./components/HostInfo"
import { MapInfo } from "./components/MapInfo"
import { StateLine } from "./components/StateLine"
import * as styles from "./eventThumbnail.module.css"

export interface ICEventHost {
  id: number
  name: string
  image: string | null
}

interface EventThumbnailProps {
  event: ICEvent
}
export const EventThumbnail = ({ event }: EventThumbnailProps) => {
  return (
    <Card className={styles.card}>
      <CardMedia
        component="img"
        className={styles.image}
        src={createImageLink("event", "large", event.id, event.image)}
      />
      <CardContent>
        <Box display="flex" flexDirection="column" gap={2}>
          <Box display="flex" gap={1} alignItems="center" flexWrap="nowrap">
            <AccessTimeIcon fontSize="medium" />
            <Typography variant="h6">
              {localToLocalWithDay(event.starts, true)}
            </Typography>
          </Box>
          {event.ends && (
            <Box display="flex" gap={1} alignItems="center" flexWrap="nowrap">
              Till <AccessTimeIcon fontSize="small" />
              <Typography variant="subtitle1">
                {localToLocalWithDay(event.ends, true)}
              </Typography>
            </Box>
          )}
          <MapInfo event={event} />
          <Box>
            <Typography variant="h2" fontSize="1.2rem">
              {event.title}
            </Typography>
          </Box>
          {event.smallDescription && (
            <Box>
              <Typography variant="body1">{event.smallDescription}</Typography>
            </Box>
          )}
          <HostInfo event={event} />
        </Box>
      </CardContent>
      <CardActions>
        <Box display="flex" flexDirection="column" gap={1} width="100%">
          <StateLine event={event} />
          <Box className={styles.bottom}>
            <EventButtons event={event} source="card" />
            <EventStatus event={event} />
          </Box>
        </Box>
      </CardActions>
    </Card>
  )
}
