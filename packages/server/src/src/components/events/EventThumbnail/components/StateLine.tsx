import { Box, Typography } from "@mui/material"

import { ICEvent } from "../../../../types/tRPC.types"
import * as styles from "../eventThumbnail.module.css"

type StateLineProps = {
  event: ICEvent
}

export const StateLine = ({ event }: StateLineProps) => {
  let color: string

  switch (event.state) {
    case "hidden":
      color = "error"
      break
    case "open":
      color = "success"
      break
    case "ongoing":
      color = "info"
      break
    case "cancelled":
    case "ended":
    default:
      color = "textDisabled"
      break
  }

  return (
    <Box
      display="flex"
      flexDirection="row"
      gap={1}
      justifyContent="space-between"
      alignItems="center"
    >
      <Box display="flex" flexDirection="row" gap={2} alignItems="center">
        <Typography variant="body1" className={styles.state} color={color}>
          {event.state}
        </Typography>
        <Typography variant="body1">
          {`Going: ${event.going} ${event.maxCapacity ? `/${event.maxCapacity}` : ""}`}
        </Typography>
      </Box>
      <Box>
        <Typography variant="body1">
          {`${event.openness.toLocaleUpperCase()}`}
        </Typography>
      </Box>
    </Box>
  )
}
