import { Box, Typography } from "@mui/material"

import { createImageLink } from "../../../../config/images"
import { COMMUNITY_ROUTE } from "../../../../routes/paths"
import { ICEvent } from "../../../../types/tRPC.types"
import { PartyLink } from "../../../elements/link/PartyLink/PartyLink"
import { UserAvatar } from "../../../user/UserAvatar/UserAvatar"
import * as styles from "../eventThumbnail.module.css"

interface HostInfoProps {
  event: ICEvent
}
export const HostInfo = ({ event }: HostInfoProps) => {
  return (
    event.hostName && (
      <Box
        display="flex"
        flexDirection="row"
        gap={0.5}
        flexWrap="wrap"
        alignItems="center"
        justifyContent="flex-start"
      >
        <Box
          display="flex"
          flexDirection="row"
          gap={0.5}
          alignItems="center"
          flexWrap="nowrap"
        >
          <Typography variant="body2" className={styles.host}>
            Host:
          </Typography>
          <UserAvatar
            user={{
              name: event.hostName,
              id: event.hostId ?? 0,
              avatar: event.hostAvatar ?? null,
              color: event.hostColor ?? null,
            }}
            labelInfo={"Host: "}
            size="small"
          />
          <Typography variant="body2" className={styles.host}>
            {event.hostName}
          </Typography>
        </Box>
        {event.communities.length > 0 && (
          <>
            <PartyLink
              to={COMMUNITY_ROUTE}
              color="info"
              variant="text"
              className={styles.community}
              sx={{ minHeight: 0, minWidth: 0, padding: 0 }}
              params={{ communityId: String(event.communities[0].id) }}
            >
              <img
                src={createImageLink(
                  "community",
                  "small",
                  event.communities[0].id,
                  event.communities[0].image,
                )}
                alt={event.communities[0].name}
                title={event.communities[0].name}
                className={styles.communityImage}
              />
              {`${event.communities[0].name}`}
            </PartyLink>
          </>
        )}
      </Box>
    )
  )
}
