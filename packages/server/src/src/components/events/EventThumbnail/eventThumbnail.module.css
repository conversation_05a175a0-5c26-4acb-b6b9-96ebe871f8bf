.bottom {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.role {
  padding: var(--spacing-h);
  border-radius: var(--spacing-1);
    display: flex;
  flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
  gap: var(--spacing-h);
}

.location {
    line-clamp: 1;
    overflow: hidden;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

.mapButton {
    white-space: nowrap;
}

.state {
    text-transform: capitalize;
    font-weight: 600;
}

.card {
    display: flex;
    flex-direction: column;
    width: 350px;
    justify-content: space-between;
}

.host, .community {
    text-overflow: ellipsis;
    text-wrap-mode: nowrap;
    overflow: hidden;
    max-width: 50%;
}

.community {
    display: flex;
    flex-direction: row;
    gap: var(--spacing-h);
}

.image {
    height: 200px;
    object-fit: cover;
}

.communityImage {
    width: 20px;
    height: 20px;
    object-fit: contain;
}