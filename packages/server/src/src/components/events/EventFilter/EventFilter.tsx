import { Box, Button, ButtonGroup } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback } from "react"

import { COMMUNITY_EVENTS_ROUTE, EVENTS_ROUTE } from "../../../routes/paths"

type EventFilterProps = {
  from: typeof COMMUNITY_EVENTS_ROUTE | typeof EVENTS_ROUTE
  search: {
    period?: "past" | "future" | "current" | "active"
    owned?: "host" | "my" | "all"
  }
}
export const EventFilter = ({ from, search }: EventFilterProps) => {
  const navigate = useNavigate({ from })

  const onPeriodChange = useCallback(
    (period: "past" | "future" | "current" | "active") => {
      navigate({
        search: {
          ...search,
          period,
        },
      })

      window.scrollTo({ top: 0 })
    },
    [navigate, search],
  )

  const onOwnedChange = useCallback(
    (owned: "host" | "my" | "all") => {
      navigate({
        search: {
          ...search,
          owned,
        },
      })

      window.scrollTo({ top: 0 })
    },
    [navigate, search],
  )

  return (
    <Box display="flex" flexDirection="row" gap={2}>
      <ButtonGroup>
        <Button
          variant="outlined"
          onClick={() => onOwnedChange("all")}
          title="Owned events"
          aria-label="Owned events"
          disabled={!search.owned || search.owned === "all"}
        >
          All
        </Button>
        <Button
          variant="outlined"
          onClick={() => onOwnedChange("my")}
          title="My events"
          aria-label="My events"
          disabled={search.owned === "my"}
        >
          My
        </Button>
        <Button
          variant="outlined"
          onClick={() => onOwnedChange("host")}
          title="Hosted events"
          aria-label="Hosted events"
          disabled={search.owned === "host"}
        >
          Hosted
        </Button>
      </ButtonGroup>
      <ButtonGroup title="Event period filter controls">
        <Button
          variant="outlined"
          onClick={() => onPeriodChange("active")}
          title="Active events"
          aria-label="Active events"
          disabled={!search.period || search.period === "active"}
        >
          Active
        </Button>
        <Button
          variant="outlined"
          onClick={() => onPeriodChange("future")}
          title="Future events"
          aria-label="Future events"
          disabled={search.period === "future"}
        >
          Future
        </Button>
        <Button
          variant="outlined"
          onClick={() => onPeriodChange("past")}
          title="Past events"
          aria-label="Past events"
          disabled={search.period === "past"}
        >
          Past
        </Button>
      </ButtonGroup>
    </Box>
  )
}
