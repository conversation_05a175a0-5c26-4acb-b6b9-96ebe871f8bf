import { Box, FormControl, TextField } from "@mui/material"
import debounce from "debounce"
import {
  type ChangeEvent,
  type KeyboardEvent,
  memo,
  useMemo,
  useState,
} from "react"

interface SearchBoxProps {
  onSearch: (search: string) => void
  search: string | undefined
}
export const SearchBox = memo(({ onSearch, search }: SearchBoxProps) => {
  const [searchText, setSearchText] = useState<string | undefined>(search ?? "")

  const handleSearch = (event: ChangeEvent<HTMLInputElement>) =>
    onSearch(event.target.value)

  const onSearchDelayed = useMemo(() => debounce(handleSearch, 200), [onSearch])

  const onSearchCommon = (event: ChangeEvent<HTMLInputElement>) => {
    onSearchDelayed(event)
    setSearchText(event.target.value)
  }

  const onKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Escape") {
      setSearchText("")
      onSearch("")
    }
  }

  return (
    <Box position="relative">
      <FormControl fullWidth>
        <TextField
          value={searchText}
          id="search-events"
          label="Search"
          variant="outlined"
          onChange={onSearchCommon}
          onKeyDown={onKeyDown}
        />
      </FormControl>
    </Box>
  )
})
