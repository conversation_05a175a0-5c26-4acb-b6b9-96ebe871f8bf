import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { Al<PERSON>, Box, Button } from "@mui/material"
import { memo, useCallback, useState } from "react"
import { FormProvider, type SubmitHandler, useForm } from "react-hook-form"
import { z } from "zod"

import { useFirebase } from "../../../hooks/useFirebase"
import { useModalStore } from "../../../store/useModalStore"
import { FormInput } from "../../elements/HookElements/FormInput"
import { Modal } from "../Modal"
import { REGISTER_MODAL_NAME } from "../RegisterModal/RegisterModal"

export const LOGIN_MODAL_NAME = "login"

export const loginInputSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
})

type LoginInput = z.infer<typeof loginInputSchema>

export const LoginModal = memo(() => {
  const [error, setError] = useState<string | null>(null)
  const { loginWithGoogle, loginWithEmail } = useFirebase()

  // Get functions separately to avoid including them in the reactive selector
  const { closeModal, openModal } = useModalStore()

  const methods = useForm<LoginInput>({
    resolver: zodResolver(loginInputSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  const handleGoogleLogin = useCallback(async () => {
    loginWithGoogle()
    closeModal(LOGIN_MODAL_NAME)
  }, [closeModal])

  const handleEmailLogin: SubmitHandler<LoginInput> = useCallback(
    async (data) => {
      try {
        await loginWithEmail(data.email, data.password)
        closeModal(LOGIN_MODAL_NAME)
      } catch (errorMessage: unknown) {
        const error = errorMessage as { message: string }
        setError(error.message ?? "Unknown error")
        console.error("Login failed:", error)
      }
    },
    [loginWithEmail, closeModal, setError],
  )

  const handleRegister = () => {
    closeModal(LOGIN_MODAL_NAME)
    openModal(REGISTER_MODAL_NAME)
  }

  return (
    <Modal name={LOGIN_MODAL_NAME} title="Login">
      <Box
        display="flex"
        justifyContent="center"
        flexDirection="column"
        gap={4}
        alignItems="center"
      >
        <Button
          variant="contained"
          color="primary"
          onClick={() => handleGoogleLogin()}
        >
          Sign on/Log In With Google
        </Button>

        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(handleEmailLogin)}>
            <Box display="flex" flexDirection="column" gap={2}>
              <FormInput
                label="Email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
              />
              <FormInput label="Password" name="password" type="password" />
              {error && <Alert color="error">{error}</Alert>}
              <Button type="submit" variant="outlined" color="primary">
                Login
              </Button>
            </Box>
          </form>
        </FormProvider>
        <Button onClick={handleRegister} variant="contained" color="secondary">
          Register
        </Button>
      </Box>
    </Modal>
  )
})
