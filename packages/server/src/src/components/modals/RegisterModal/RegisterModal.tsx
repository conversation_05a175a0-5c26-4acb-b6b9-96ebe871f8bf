import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON>, Box, Button } from "@mui/material"
import { createUserWithEmailAndPassword, getAuth } from "firebase/auth"
import { memo, useState } from "react"
import { <PERSON><PERSON><PERSON>ider, type SubmitHandler, useForm } from "react-hook-form"
import { z } from "zod"

import { useModalStore } from "../../../store/useModalStore"
import { firebaseApp } from "../../../utils/firebase"
import { FormInput } from "../../elements/HookElements/FormInput"
import { Modal } from "../Modal"

export const REGISTER_MODAL_NAME = "register"

export const loginInputSchema = z
  .object({
    email: z.string().email(),
    password: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z
      .string()
      .min(8, "Confirm Password must be at least 8 characters"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"], // This will attach the error to confirmPassword
  })

type LoginInput = z.infer<typeof loginInputSchema>

export const RegisterModal = memo(() => {
  const [error, setError] = useState<string | null>(null)

  const auth = getAuth(firebaseApp)

  const { closeModal } = useModalStore()

  const methods = useForm<LoginInput>({
    resolver: zodResolver(loginInputSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  const handleEmailRegister: SubmitHandler<LoginInput> = async (data) => {
    try {
      await createUserWithEmailAndPassword(auth, data.email, data.password)
      closeModal(REGISTER_MODAL_NAME)
    } catch (errorMessage: unknown) {
      const error = errorMessage as { message: string }
      setError(error.message ?? "Unknown error")
      console.error("Login failed:", error)
    }
  }

  return (
    <Modal name={REGISTER_MODAL_NAME} title="Register">
      <Box
        display="flex"
        justifyContent="center"
        flexDirection="column"
        gap={4}
        alignItems="center"
      >
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(handleEmailRegister)}>
            <Box display="flex" flexDirection="column" gap={2}>
              <FormInput
                label="Email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
              />
              <FormInput label="Password" type="password" name="password" />
              <FormInput
                label="Confirm Password"
                name="confirmPassword"
                type="password"
              />
              {error && <Alert color="error">{error}</Alert>}
              <Button type="submit" variant="contained" color="primary">
                Register
              </Button>
            </Box>
          </form>
        </FormProvider>
      </Box>
    </Modal>
  )
})
