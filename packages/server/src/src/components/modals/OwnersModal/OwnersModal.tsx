import { useUserStore } from "../../../store/useUserStore"
import { ownerSort } from "../../../utils/ownerSort"
import { OwnersList } from "../../user/OwnersList/OwnersList"
import { Modal } from "../Modal"

import type { IGameGameUser } from "../../../types/tRPC.types"

interface OwnersListProps {
  userList: IGameGameUser[]
  onViewUser: (user?: number) => void
  onOpenUserProfile: (user: number) => void
}

export const OWNERS_MODAL_NAME = "owners"
export const OwnersModal = ({
  onViewUser,
  onOpenUserProfile,
  userList,
}: OwnersListProps) => {
  const myUserId = useUserStore((state) => state.userData.id)
  const users = userList.sort(ownerSort(myUserId ?? 0))

  return (
    <Modal name={OWNERS_MODAL_NAME} title="Owners">
      <OwnersList
        userList={users}
        onViewUser={onViewUser}
        onOpenUserProfile={onOpenUserProfile}
      />
    </Modal>
  )
}
