import { Box, Typography } from "@mui/material"

import {
  DEFAULT_LAT,
  DEFAULT_LNG,
  DEFAULT_LOCATION,
} from "../../../config/maps"
import { ICEvent } from "../../../types/tRPC.types"
import { GMap } from "../../common/Map/GMap"
import { Modal } from "../Modal"

export const MAP_MODAL_NAME = "map"

type MapModalProps = {
  event?: Pick<ICEvent, "location" | "lat" | "lng">
  onChange?: (
    location: string | null,
    lat: number | null,
    lng: number | null,
  ) => void
}

export const MapModal = ({ event, onChange }: MapModalProps) => (
  <Modal name={MAP_MODAL_NAME} title="Map">
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      flexDirection="column"
      gap={2}
    >
      <Box width="500px" height="600px" maxWidth="90vw" maxHeight="90vh">
        <GMap
          place={event?.location ?? DEFAULT_LOCATION}
          lat={event?.lat ?? DEFAULT_LAT}
          lng={event?.lng ?? DEFAULT_LNG}
          onPlaceSelect={onChange}
        />
      </Box>
      <Typography variant="body1" color="text.secondary" align="center">
        {event?.location || "No location"}
      </Typography>
    </Box>
  </Modal>
)
