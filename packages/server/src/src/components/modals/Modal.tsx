import { <PERSON>, <PERSON><PERSON>, Dialog, <PERSON>alogContent, DialogTitle } from "@mui/material"
import classnames from "classnames"
import { PropsWithChildren, useEffect } from "react"

import { useModalStore } from "../../store/useModalStore"

import * as styles from "./modal.module.css"

type ModalProps = {
  name: string
  title: React.ReactNode
  allowOverflow?: boolean
} & PropsWithChildren

export const Modal = ({
  children,
  name,
  title,
  allowOverflow = false,
}: ModalProps) => {
  // Only select the isOpen value to prevent unnecessary rerenders
  const isOpen = useModalStore(
    (state) => state.modalList[name]?.isOpen ?? false,
  )

  // Get functions separately to avoid including them in the reactive selector
  const { createModal, closeModal } = useModalStore()

  useEffect(() => {
    createModal(name)
  }, [])

  return (
    <Dialog
      open={isOpen}
      onClose={() => {
        closeModal(name)
      }}
      PaperProps={{
        className: classnames({
          [styles.overflow]: allowOverflow,
        }),
      }}
    >
      <DialogTitle>{title}</DialogTitle>
      <DialogContent
        className={classnames({
          [styles.overflow]: allowOverflow,
        })}
      >
        <Box className={styles.close}>
          <Button
            variant="text"
            onClick={() => {
              closeModal(name)
            }}
          >
            Close
          </Button>
        </Box>
        {children}
      </DialogContent>
    </Dialog>
  )
}
