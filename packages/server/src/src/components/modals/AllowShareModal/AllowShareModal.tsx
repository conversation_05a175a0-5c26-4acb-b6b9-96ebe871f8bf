import { <PERSON>, <PERSON><PERSON>, FormControlLabel, Switch } from "@mui/material"
import { ChangeEvent } from "react"

import { Modal } from "../Modal"
import { SEARCH_MODAL_NAME } from "../SearchModal/SearchModal"

export const ALLOW_SHARE_MODAL_NAME = "allowShare"

interface AllowShareProps {
  onContinue: () => void
  onChange: (value: boolean) => void
}
export const AllowShareModal = ({ onChange, onContinue }: AllowShareProps) => (
  <Modal name={SEARCH_MODAL_NAME} title="Join community" allowOverflow>
    <FormControlLabel
      control={
        <Switch
          onChange={(_: ChangeEvent<HTMLInputElement>, value) =>
            onChange(value)
          }
        />
      }
      label="Share my collection"
    />
    <Box>
      <Button onClick={onContinue}>Continue</Button>
    </Box>
  </Modal>
)
