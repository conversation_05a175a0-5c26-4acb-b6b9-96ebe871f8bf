import { Box } from "@mui/material"
import { useEffect, useState } from "react"

import { trpc } from "../../../trpc/trpc"
import { Loader } from "../../elements/Loader/Loader"
import { GameInfo } from "../../games/GameInfo/GameInfo"
import { Modal } from "../Modal"

import type { IItemInfo } from "../../../types/tRPC.types"

export const ITEM_INFO_MODAL_NAME = "itemInfo"

interface ItemInfoModalProps {
  itemId: number | null
}
export const ItemInfoModal = ({ itemId }: ItemInfoModalProps) => {
  const [isLoading, setIsLoading] = useState(true)
  const [item, setItem] = useState<IItemInfo | null>(null)

  useEffect(() => {
    if (!itemId) {
      return
    }
    setIsLoading(true)
    trpc.getItemInfo.query({ itemId }).then((item) => {
      setItem(item)
      setIsLoading(false)
    })
  }, [itemId, setIsLoading])

  if (isLoading || !item) {
    return (
      <Modal name={ITEM_INFO_MODAL_NAME} title="Loading...">
        <Loader />
      </Modal>
    )
  }

  return (
    <Modal
      name={ITEM_INFO_MODAL_NAME}
      title={<Box pr={6}>{`${item.item.title}`}</Box>}
    >
      {isLoading && <Loader />}
      <GameInfo game={item.item} full />
    </Modal>
  )
}
