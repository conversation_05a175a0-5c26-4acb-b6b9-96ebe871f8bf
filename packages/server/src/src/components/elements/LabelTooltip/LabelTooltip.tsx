import HelpOutlineIcon from "@mui/icons-material/HelpOutline"
import { Box, Tooltip, type TooltipProps } from "@mui/material"
import { PropsWithChildren } from "react"

type LabelTooltipProps = {} & PropsWithChildren &
  Omit<TooltipProps, "title" | "children">

export const LabelTooltip = ({ children, ...props }: LabelTooltipProps) => {
  return (
    <Tooltip
      placement="top"
      arrow
      enterTouchDelay={0}
      title={<Box sx={{ color: "white" }}>{children}</Box>}
      {...props}
      PopperProps={{ sx: { color: "red" } }}
    >
      <HelpOutlineIcon fontSize="small" />
    </Tooltip>
  )
}
