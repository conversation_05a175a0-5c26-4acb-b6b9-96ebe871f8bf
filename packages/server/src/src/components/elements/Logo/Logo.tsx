import { INDEX_ROUTE } from "../../../routes/paths"
import { PartyLink } from "../link/PartyLink/PartyLink"

type LogoProps = Partial<React.ComponentProps<typeof PartyLink>>
export const Logo = ({ ...props }: LogoProps) => {
  return (
    <PartyLink
      variant="text"
      color="inherit"
      sx={{
        fontSize: "1.25rem",
        fontWeight: "extraBold",
        textTransform: "none",
      }}
      {...props}
      to={INDEX_ROUTE}
    >
      eXg
    </PartyLink>
  )
}
