import { Button } from "@mui/material"
import { createLink } from "@tanstack/react-router"
import { forwardRef } from "react"

import type { ButtonProps } from "@mui/material"
import type { LinkComponent } from "@tanstack/react-router"

type MUILinkProps = Omit<ButtonProps, "href">

const MUILinkComponent = forwardRef<HTMLAnchorElement, MUILinkProps>(
  (props, ref) => {
    return <Button component={"a"} ref={ref} {...props} />
  },
)

const CreatedLinkComponent = createLink(MUILinkComponent)

export const PartyLink: LinkComponent<typeof MUILinkComponent> = (props) => {
  return <CreatedLinkComponent preload={"intent"} {...props} />
}
