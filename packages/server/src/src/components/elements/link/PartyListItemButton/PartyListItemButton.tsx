import { ListItemButton } from "@mui/material"
import { createLink } from "@tanstack/react-router"
import { forwardRef } from "react"

import type { ListItemButtonProps } from "@mui/material"
import type { LinkComponent } from "@tanstack/react-router"

type MUILinkProps = Omit<ListItemButtonProps, "href">

const MUILinkComponent = forwardRef<HTMLAnchorElement, MUILinkProps>(
  (props, ref) => {
    return <ListItemButton component={"a"} ref={ref} {...props} />
  },
)

const CreatedLinkComponent = createLink(MUILinkComponent)

export const PartyListItemButton: LinkComponent<typeof MUILinkComponent> = (
  props,
) => {
  return <CreatedLinkComponent preload={"intent"} {...props} />
}
