import { Box, type BoxProps } from "@mui/material"
import { type LinkComponent, createLink } from "@tanstack/react-router"
import { forwardRef, useMemo } from "react"

const MuiBoxComponent = forwardRef<HTMLAnchorElement, BoxProps>(
  (props, ref) => {
    return <Box component={"div"} ref={ref} {...props} />
  },
)

const CreatedBoxComponent = createLink(MuiBoxComponent)

export const PartyBox: LinkComponent<typeof MuiBoxComponent> = (props) => {
  return useMemo(
    () => <CreatedBoxComponent preload={"intent"} {...props} />,
    [props],
  )
}
