import { Box, Typography } from "@mui/material"
import { LocalizationProvider, MobileDateTimePicker } from "@mui/x-date-pickers"
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs"
import dayjs from "dayjs"
import { type FC } from "react"
import { Controller, useFormContext } from "react-hook-form"

import { dateTimeISO8601Format } from "../../../utils/transformTime"

interface FormInputProps {
  label: string
  name: string
  helper?: string
  required?: boolean
}

// TODO implement time zone (react-timezone-select ? )
export const FormDateTime: FC<FormInputProps> = ({
  label,
  name,
  helper = "",
  required = false,
}) => {
  const { control } = useFormContext()
  return (
    <Controller
      name={name}
      control={control}
      rules={{ required }}
      render={({ field: { onChange, value } }) => (
        <>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <MobileDateTimePicker
              label={label}
              onChange={(value) =>
                onChange(value?.format(dateTimeISO8601Format))
              }
              value={value ? dayjs(value) : undefined}
              minutesStep={5}
              disablePast
              ampm={false}
            />
          </LocalizationProvider>
          {helper && (
            <Box>
              <Typography variant="caption" color="textSecondary">
                {helper}
              </Typography>
            </Box>
          )}
        </>
      )}
    />
  )
}
