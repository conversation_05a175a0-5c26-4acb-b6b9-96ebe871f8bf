import { Box, FormControlLabel, Switch, Typography } from "@mui/material"
import { type FC } from "react"
import { Controller, useFormContext } from "react-hook-form"

interface FormInputProps {
  label: string
  name: string
  helper?: string
  required?: boolean
}

export const FormSwitch: FC<FormInputProps> = ({
  label,
  name,
  helper = "",
  required = false,
}) => {
  const { control } = useFormContext()
  return (
    <Controller
      name={name}
      control={control}
      rules={{ required }}
      render={({ field: { onChange, value } }) => (
        <>
          <FormControlLabel
            control={<Switch onChange={onChange} checked={value} />}
            label={label}
          />
          {helper && (
            <Box>
              <Typography variant="caption" color="textSecondary">
                {helper}
              </Typography>
            </Box>
          )}
        </>
      )}
    />
  )
}
