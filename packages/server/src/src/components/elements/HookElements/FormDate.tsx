import { Box, Typography } from "@mui/material"
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers"
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs"
import dayjs from "dayjs"
import { type FC } from "react"
import { Controller, useFormContext } from "react-hook-form"

import { datePostFormat } from "../../../utils/transformTime"

interface FormInputProps {
  label: string
  name: string
  helper?: string
  required?: boolean
}

export const FormDate: FC<FormInputProps> = ({
  label,
  name,
  helper = "",
  required = false,
}) => {
  const { control } = useFormContext()
  return (
    <Controller
      name={name}
      control={control}
      rules={{ required }}
      render={({ field: { onChange, value } }) => (
        <>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              label={label}
              onChange={(value) => onChange(value?.format(datePostFormat))}
              value={dayjs(value)}
              disablePast
            />
          </LocalizationProvider>
          {helper && (
            <Box>
              <Typography variant="caption" color="textSecondary">
                {helper}
              </Typography>
            </Box>
          )}
        </>
      )}
    />
  )
}
