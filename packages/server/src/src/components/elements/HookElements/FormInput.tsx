import {
  Box,
  FormControl,
  TextField,
  type TextFieldProps,
  Typography,
} from "@mui/material"
import { type ChangeEvent, type FC } from "react"
import { Controller, useFormContext } from "react-hook-form"

type FormInputProps = TextFieldProps & {
  name: string
  helper?: string
  numeric?: boolean
}

export const FormInput: FC<FormInputProps> = ({
  name,
  helper = "",
  multiline = false,
  required = false,
  type = "text",
  ...props
}) => {
  const { control } = useFormContext()
  return (
    <Controller
      name={name ?? ""}
      control={control}
      rules={{ required }}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <>
          <FormControl error={!!error} fullWidth>
            <TextField
              helperText={error ? error.message : null}
              size="small"
              error={!!error}
              onChange={(event: ChangeEvent<HTMLInputElement>) =>
                onChange(
                  type === "number"
                    ? parseInt(event.target.value)
                    : event.target.value,
                )
              }
              value={value}
              fullWidth
              type={type}
              multiline={multiline}
              rows={multiline ? 4 : 1}
              variant="outlined"
              {...props}
            />
          </FormControl>
          {helper && (
            <Box>
              <Typography variant="caption" color="textSecondary">
                {helper}
              </Typography>
            </Box>
          )}
        </>
      )}
    />
  )
}
