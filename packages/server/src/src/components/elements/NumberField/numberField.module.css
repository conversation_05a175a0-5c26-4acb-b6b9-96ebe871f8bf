@value isTablet, isLargeTablet, isSmallDesktop from "../../../css/size.module.css";

.container {
  display: flex;
  flex-direction: row;
}

.inputContainer {
  display: flex;
  flex-direction: column-reverse;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
}

.button {
  width: 30px;
  min-width: 30px;
  border-radius: 0;
  border: 1px solid;
  color: black;
  padding: 0 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #eee;
  transition: all 0.2s ease;
}

.button:focus {
  outline: none;
}

.button:active {
  background-color: #ccc;
}

.button:disabled {
  background-color: #eee;
  color: #ccc;
}

.button:hover {
  background-color: #ddd;
}

.plus {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.minus {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

.input {
  width: 30px;
  text-align: center;
  -moz-appearance: textfield; /* Firefox */
}

.input::-webkit-outer-spin-button,
.input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.label {
  font-size: 0.7rem;
  text-orientation: upright;
  writing-mode: vertical-lr;
  flex-grow: 1;
  text-align: center;
  user-select: none;
}

@media (max-width: isLargeTablet) {
  .container {
    flex-direction: column;
  }

  .label {
    text-orientation: mixed;
    writing-mode: horizontal-tb;
    font-size: 1rem;
  }

  .inputContainer {
    flex-direction: row;
  }

  .plus {
    border-radius: 0 8px 8px 0;
  }

  .minus {
    border-radius: 8px 0 0 8px;
  }

  .button {
    height: 40px;
    width: 40px;
    padding: 0 10px;
  }

  .input {
    width: 40px;
    height: 34px;
  }
}
