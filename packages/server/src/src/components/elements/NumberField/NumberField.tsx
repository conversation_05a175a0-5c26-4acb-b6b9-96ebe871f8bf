import { Box, Typography } from "@mui/material"
import classNames from "classnames"

import * as styles from "./numberField.module.css"

type NumberFieldProps = {
  value: number
  label: string
  min?: number
  max?: number
  onChangeValue: (value: number) => void
} & React.InputHTMLAttributes<HTMLInputElement>

export const NumberField = ({
  value,
  onChangeValue,
  label,
  min = 0,
  max = 100,
  ...props
}: NumberFieldProps) => {
  return (
    <Box className={styles.container}>
      <Typography variant="body1" className={styles.label}>
        {label}
      </Typography>
      <Box className={styles.inputContainer}>
        <button
          className={classNames(styles.button, styles.minus)}
          onClick={() => onChangeValue(value - 1)}
          disabled={value <= min}
        >
          -
        </button>
        <input
          min={min}
          max={max}
          className={styles.input}
          type="number"
          value={value}
          onChange={(event) => onChangeValue(parseInt(event.target.value))}
          {...props}
        />
        <button
          className={classNames(styles.button, styles.plus)}
          onClick={() => onChangeValue(value + 1)}
          disabled={value >= max}
        >
          +
        </button>
      </Box>
    </Box>
  )
}
