import { Box, Typography } from "@mui/material"
import classNames from "classnames"
import React, { type FC } from "react"

import * as styles from "./titleRow.module.css"

interface TitleRowProps {
  title: React.ReactNode
  children?: React.ReactNode
  className?: string
}
export const TitleRow: FC<TitleRowProps> = ({ className, children, title }) => {
  return (
    <Box className={classNames(className ?? "")}>
      <Box className={styles.titleWrapper}>
        <Box className={styles.title}>
          <Box className={styles.nameWrapper}>
            <Typography fontWeight="500" variant="h6" className={styles.name}>
              {title}
            </Typography>
            {children && <Box className={styles.childrenBox}>{children}</Box>}
          </Box>
        </Box>
      </Box>
    </Box>
  )
}
