@value isLargeTablet from "../../../css/size.module.css";

.title {
  padding: var(--spacing-1) var(--spacing-5);
  border-bottom: 1px solid #ccc;
  max-width: 100%;
  box-sizing: border-box;
}

.name {
  text-overflow: ellipsis;
  text-wrap-mode: nowrap;
  overflow: hidden;
  width: 100%;
}

.nameWrapper {
}

.titleWrapper {
  max-width: 100%;
  display: flex;
  justify-content: flex-end;
}

.titleBox {
  margin-top: var(--spacing-1);
  display: flex;
  justify-content: flex-end;
}

@media (max-width: isLargeTablet) {
  .titleBox {
    margin-top: var(--spacing-2);
  }
}

.childrenBox {
  padding-top: var(--spacing-2);
  display: flex;
  justify-content: flex-end;
}
