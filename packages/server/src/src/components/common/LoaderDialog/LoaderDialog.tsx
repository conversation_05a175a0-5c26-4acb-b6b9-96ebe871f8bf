import {
  <PERSON><PERSON>,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material"

import { Loader } from "../../elements/Loader/Loader"

export type LoaderDialogState = "loading" | "done" | "failed" | null

interface LoaderDialogProps {
  state: LoaderDialogState
  successMessage?: string
  failMessage?: string
  onCloseSuccess?: () => void
  onCloseFail?: () => void
  onClose?: () => void
  title?: string
}
export const LoaderDialog = ({
  state,
  onCloseSuccess,
  onCloseFail,
  onClose,
  successMessage,
  failMessage,
  title,
}: LoaderDialogProps) => {
  const handleCloseDialog = () => {
    if (state === "done" && onCloseSuccess) {
      onCloseSuccess()
    }
    if (state === "failed" && onCloseFail) {
      onCloseFail()
    }
    if (onClose) {
      onClose()
    }
  }

  return (
    <Dialog open={!!state}>
      {title && <DialogTitle>{title}</DialogTitle>}
      <DialogContent>
        {state === "loading" && <Loader />}
        {state === "done" && (
          <Alert severity="success">{successMessage ?? "Success"}</Alert>
        )}
        {state === "failed" && (
          <Alert severity="error">{failMessage ?? "Task failed!"}</Alert>
        )}
      </DialogContent>
      {((state === "done" && onCloseSuccess) ||
        (state === "failed" && onCloseFail) ||
        (state !== "loading" && onClose)) && (
        <DialogActions>
          <Button onClick={handleCloseDialog}>Ok</Button>
        </DialogActions>
      )}
    </Dialog>
  )
}
