import { <PERSON>, Button, Text<PERSON>ield, Typo<PERSON> } from "@mui/material"
import { APIProvider } from "@vis.gl/react-google-maps"
import { useState } from "react"

import { usePlaceSearch } from "./usePlacesSearch"

export const PlaceSearchExample = () => {
  const { findPlaceByName, findPlaceByCoordinates } = usePlaceSearch()
  const [placeName, setPlaceName] = useState("")
  const [latitude, setLatitude] = useState("")
  const [longitude, setLongitude] = useState("")
  const [foundPlace, setFoundPlace] = useState<google.maps.places.Place | null>(null)
  const [loading, setLoading] = useState(false)

  const handleSearchByName = async () => {
    if (!placeName.trim()) return
    
    setLoading(true)
    try {
      const place = await findPlaceByName(placeName)
      setFoundPlace(place)
    } catch (error) {
      console.error("Error searching by name:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearchByCoordinates = async () => {
    const lat = parseFloat(latitude)
    const lng = parseFloat(longitude)
    
    if (isNaN(lat) || isNaN(lng)) {
      alert("Please enter valid coordinates")
      return
    }
    
    setLoading(true)
    try {
      const place = await findPlaceByCoordinates(lat, lng)
      setFoundPlace(place)
    } catch (error) {
      console.error("Error searching by coordinates:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <APIProvider
      apiKey="AIzaSyC6ehAxduYC4T7K3eQYiFLTZuP4n2rvpAo"
      libraries={["places"]}
    >
      <Box p={3} maxWidth={600}>
        <Typography variant="h5" gutterBottom>
          Google Maps Place Search Example
        </Typography>
        
        {/* Search by Place Name */}
        <Box mb={3}>
          <Typography variant="h6" gutterBottom>
            Search by Place Name
          </Typography>
          <Box display="flex" gap={2} alignItems="center">
            <TextField
              label="Place Name"
              value={placeName}
              onChange={(e) => setPlaceName(e.target.value)}
              placeholder="e.g., Eiffel Tower, Paris"
              fullWidth
            />
            <Button
              variant="contained"
              onClick={handleSearchByName}
              disabled={loading || !placeName.trim()}
            >
              Search
            </Button>
          </Box>
        </Box>

        {/* Search by Coordinates */}
        <Box mb={3}>
          <Typography variant="h6" gutterBottom>
            Search by Coordinates
          </Typography>
          <Box display="flex" gap={2} alignItems="center">
            <TextField
              label="Latitude"
              value={latitude}
              onChange={(e) => setLatitude(e.target.value)}
              placeholder="e.g., 48.8584"
              type="number"
            />
            <TextField
              label="Longitude"
              value={longitude}
              onChange={(e) => setLongitude(e.target.value)}
              placeholder="e.g., 2.2945"
              type="number"
            />
            <Button
              variant="contained"
              onClick={handleSearchByCoordinates}
              disabled={loading || !latitude || !longitude}
            >
              Search
            </Button>
          </Box>
        </Box>

        {/* Results */}
        {loading && (
          <Typography>Searching...</Typography>
        )}
        
        {foundPlace && !loading && (
          <Box mt={3} p={2} border={1} borderColor="grey.300" borderRadius={1}>
            <Typography variant="h6" gutterBottom>
              Found Place:
            </Typography>
            <Typography><strong>Name:</strong> {foundPlace.displayName}</Typography>
            <Typography><strong>Address:</strong> {foundPlace.formattedAddress}</Typography>
            <Typography><strong>Place ID:</strong> {foundPlace.id}</Typography>
            {foundPlace.location && (
              <Typography>
                <strong>Coordinates:</strong> {foundPlace.location.lat()}, {foundPlace.location.lng()}
              </Typography>
            )}
            <Typography><strong>Types:</strong> {foundPlace.types?.join(", ")}</Typography>
          </Box>
        )}
        
        {!foundPlace && !loading && placeName && (
          <Typography color="error">No place found</Typography>
        )}
      </Box>
    </APIProvider>
  )
}
