import { <PERSON><PERSON><PERSON><PERSON>, Pin, useMap } from "@vis.gl/react-google-maps"
import React, { useEffect } from "react"

import { usePlaceSearch } from "./usePlacesSearch"

interface Props {
  place: google.maps.places.Place | null
  onDrag: (place: google.maps.places.Place) => void
  draggable?: boolean
}

export const AutocompleteResult = ({ place, onDrag, draggable }: Props) => {
  const { findPlaceByCoordinates } = usePlaceSearch()
  const map = useMap()

  const handleDragEnd = async (e: google.maps.MapMouseEvent) => {
    if (!e.latLng) return
    const newPlace = await findPlaceByCoordinates(
      e.latLng.lat(),
      e.latLng.lng(),
    )

    if (newPlace) onDrag(newPlace)
  }

  // adjust the viewport of the map when the place is changed
  useEffect(() => {
    if (!map || !place) return
    if (place.viewport) map.fitBounds(place.viewport)
  }, [map, place])

  if (!place) return null

  // add a marker for the selected place
  return (
    <AdvancedMarker
      position={place.location}
      draggable={draggable}
      onDragEnd={handleDragEnd}
    >
      <Pin
        background={place.iconBackgroundColor}
        glyph={place.svgIconMaskURI ? new URL(place.svgIconMaskURI) : null}
      />
    </AdvancedMarker>
  )
}

export default React.memo(AutocompleteResult)
