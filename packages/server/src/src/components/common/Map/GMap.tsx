import { Box } from "@mui/material"
import { ControlPosition, Map } from "@vis.gl/react-google-maps"
import { useEffect, useState } from "react"

import { DEFAULT_LAT, DEFAULT_LNG } from "../../../config/maps"

import { AutocompleteCustom } from "./AutocompleteControl"
import { AutocompleteResult } from "./AutocompleteResult"
import { usePlaceSearch } from "./usePlacesSearch"

type GMapProps = {
  onPlaceSelect?: (
    location: string | null,
    lat: number | null,
    lng: number | null,
  ) => void
  place: string
  lat: number
  lng: number
}

export const GMap = ({ place, lat, lng, onPlaceSelect }: GMapProps) => {
  const { findPlaceByName, findPlaceByCoordinates } = usePlaceSearch()
  const [selectedPlace, setSelectedPlace] =
    useState<google.maps.places.Place | null>(null)

  const handleChange = (place: google.maps.places.Place | null) => {
    setSelectedPlace(place)
    if (onPlaceSelect) {
      onPlaceSelect(
        `${place?.displayName && place?.formattedAddress !== place?.displayName ? `${place.displayName}, ` : ""}${place?.formattedAddress}`,
        place?.location?.lat() ?? null,
        place?.location?.lng() ?? null,
      )
    }
  }

  useEffect(() => {
    const loadPlace = async () => {
      let foundPlace: google.maps.places.Place | null = null

      if (lat && lng) {
        // Fallback to coordinates if place name doesn't work
        foundPlace = await findPlaceByCoordinates(lat, lng)
      }

      if (!foundPlace && place) {
        // Try to find place by name first
        foundPlace = await findPlaceByName(place)
      }

      if (foundPlace) {
        setSelectedPlace(foundPlace)
      }
    }

    loadPlace()
  }, [place, lat, lng, findPlaceByName, findPlaceByCoordinates])

  return (
    <Box height="100%" width="100%">
      <Map
        mapId={"bf51a910020fa25a"}
        defaultZoom={7}
        defaultCenter={{
          lat: selectedPlace?.location?.lat() ?? DEFAULT_LAT,
          lng: selectedPlace?.location?.lng() ?? DEFAULT_LNG,
        }}
        gestureHandling={"greedy"}
        disableDefaultUI
      >
        {onPlaceSelect && (
          <AutocompleteCustom
            controlPosition={ControlPosition.TOP_LEFT}
            onPlaceSelect={handleChange}
          />
        )}
        <AutocompleteResult
          place={selectedPlace}
          onDrag={handleChange}
          draggable={!!onPlaceSelect}
        />
      </Map>
    </Box>
  )
}
