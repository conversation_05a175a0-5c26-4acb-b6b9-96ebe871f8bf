import type { IGameGameUser } from "../types/tRPC.types"

export const ownerSort =
  (myUserId: number) => (t1: IGameGameUser, t2: IGameGameUser) => {
    if (t1.id === myUserId) return -1
    if (t2.id === myUserId) return 1

    const p1 = t1.experience ?? 0
    const p2 = t2.experience ?? 0

    if (p1 > p2) return -1
    if (p1 < p2) return 1
    if (p1 === p2) {
      return t1.name < t2.name ? -1 : 1
    }

    return 0
  }
