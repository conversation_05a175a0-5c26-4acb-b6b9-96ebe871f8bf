import { useParentMatches } from "@tanstack/react-router"

import { COMMUNITIES_ROOT_ROUTE, EVENT_ROOT_ROUTE } from "../routes/paths"

import type { ICommunityBaseData, IEventBaseData } from "../types/tRPC.types"

type EventRouteType = typeof EVENT_ROOT_ROUTE
type CommunityRouteType = typeof COMMUNITIES_ROOT_ROUTE

export const isCommunity = (obj: unknown): obj is ICommunityBaseData =>
  !!(obj as ICommunityBaseData)?.name

export const isEvent = (obj: unknown): obj is IEventBaseData =>
  !!(obj as IEventBaseData)?.title

export const useParentRouteData = (
  routePath: EventRouteType | CommunityRouteType,
) => {
  const { loaderData } =
    useParentMatches().find((match) => match.fullPath === routePath) ?? {}

  return loaderData
}
