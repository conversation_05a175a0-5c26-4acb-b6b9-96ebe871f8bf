import dayjs from "dayjs"

import { LAST_UPDATE_PERIOD } from "../config/game.conf"

export const dateTimeISO8601Format = "YYYY-MM-DDTHH:mm:ssZ"
export const datePostFormat = "YYYY-MM-DD"
export const dateLocal = "ll"
export const dateTimeLocal = "lll"

export const localLocalWithDay = "dd, ll"
export const localLocalTimeWithDay = "dd, lll"

export const utcToLocal = (date: string, time = false) => {
  return dayjs
    .utc(date)
    .local()
    .format(time ? dateTimeLocal : dateLocal)
}

export const localToLocalWithDay = (date: string | Date, time = false) => {
  return dayjs(date)
    .utc()
    .format(time ? localLocalTimeWithDay : localLocalWithDay)
}

export const localToPost = (date: string | Date) => {
  return dayjs(date).format(dateTimeISO8601Format)
}

export const isNewEntry = (date: string | null) => {
  return dayjs(date).isBefore(dayjs(), LAST_UPDATE_PERIOD)
}
