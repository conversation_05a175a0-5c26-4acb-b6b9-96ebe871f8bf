export type GlobalRight =
  | "superadmin"
  | "admin"
  | "moder"
  | "user"
  | "unverified"
  | "banned"

export type LocalRight =
  | "owner"
  | "moder"
  | "user"
  | "guest"
  | "invited"
  | "banned"
export const localRights: LocalRight[] = [
  "owner",
  "moder",
  "user",
  "guest",
  "invited",
  "banned",
]

export const globalRights: GlobalRight[] = [
  "superadmin",
  "admin",
  "moder",
  "user",
  "unverified",
  "banned",
]

export const canIDo = (
  right: { local: LocalRight; global: GlobalRight },
  userRight: GlobalRight,
  comunityRight: LocalRight,
  isAdminMode: boolean,
): boolean => {
  if (isAdminMode && canAdminDo(right.global, userRight, isAdminMode)) {
    return true
  }

  const requiredRightsIndex = localRights.indexOf(right.local)
  const userRightsIndex = localRights.indexOf(comunityRight)

  if (requiredRightsIndex >= userRightsIndex) {
    return true
  }

  return false
}

export const canAdminDo = (
  right: GlobalRight,
  userRight: GlobalRight,
  isAdminMode: boolean,
): boolean => {
  if (!isAdminMode) {
    return false
  }

  const requiredRightsIndex = globalRights.indexOf(right)
  const userRightsIndex = globalRights.indexOf(userRight)

  if (requiredRightsIndex >= userRightsIndex) {
    return true
  }

  return false
}

export class ShittyTRPCHeaderStorage {
  private _authToken = ""

  get authToken(): string {
    return this._authToken
  }

  set authToken(token: string) {
    this._authToken = token
  }
}

export const auth = new ShittyTRPCHeaderStorage()
