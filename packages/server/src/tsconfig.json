{
  "compilerOptions": {
    "outDir": "build",
    "skipLibCheck": true,
    "module": "ESNext",
    "target": "ES2022",
    "lib": ["ES6", "DOM", "DOM.iterable", "ESNext"],
    "sourceMap": true,
    "allowJs": false,
    "jsx": "react-jsx",
    "strict": true,
    "moduleResolution": "bundler",
    "rootDirs": ["./src", "../router", "../common"],
    "forceConsistentCasingInFileNames": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "downlevelIteration": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,
  },
  "exclude": [
    "node_modules",
    "dist",
    "vite.config.ts"
  ],
  "types": [
    "typePatches"
  ]
}