// example: make a directory in home directory such as ~/devcert-util
// ~/devcert-util/generate.js
const fs = require("fs")

const devcert = require("devcert")

// or if its just one domain - devcert.certificateFor('local.example.com')
devcert
  .certificateFor(["lparty.explain.games", "lparty-api.explain.games"])
  .then(({ key, cert }) => {
    fs.writeFileSync("./tls.key", key)
    fs.writeFileSync("./tls.cert", cert)
  })
  .catch(console.error)
