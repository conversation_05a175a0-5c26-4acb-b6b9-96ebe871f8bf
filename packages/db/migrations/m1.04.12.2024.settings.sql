-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: pr-db-mysql:3306
-- Generation Time: Dec 03, 2024 at 11:53 PM
-- Server version: 9.1.0
-- PHP Version: 8.2.8

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `party`
--

--
-- Dumping data for table `crons`
--

INSERT INTO `crons` (`id`, `last_run`, `is_on`, `refetch`, `run_period`, `is_running`, `blocking`, `items_per_batch`) VALUES
                                                                                                                          ('community_games', '2023-12-03 23:19:35', 1, 10, 5, 0, 0, 1000),
                                                                                                                          ('community_items', '2023-12-03 23:19:35', 1, 10, 5, 0, 0, 1000),
                                                                                                                          ('existing_games', '2023-12-03 23:19:35', 1, 60, 10, 0, 1, 100),
                                                                                                                          ('new_games', '2023-12-03 23:19:35', 1, 60, 10, 0, 1, 100),
                                                                                                                          ('users', '2023-12-03 23:19:35', 1, 60, 10, 0, 1, 100);

--
-- Dumping data for table `permission_roles`
--

INSERT INTO `permission_roles` (`role`, `title`, `subject`) VALUES
                                                                ('admin', 'Admin', 'global'),
                                                                ('banned', 'Banned', 'global'),
                                                                ('disgraced', 'Disgraced', 'community'),
                                                                ('invited', 'Invited', 'community'),
                                                                ('member', 'Member', 'community'),
                                                                ('moder', 'Moder', 'community'),
                                                                ('owner', 'Owner', 'community'),
                                                                ('superadmin', 'Super admin', 'global'),
                                                                ('trusted', 'Trusted user', 'global'),
                                                                ('unverified', 'Unverified', 'global'),
                                                                ('user', 'User', 'global');

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`name`, `description`, `value`) VALUES
                                                            ('cron:sleep_before_items', 'Sleep time in seconds', '1'),
                                                            ('cron:user_init_call_sleep', 'Sleep after initial request (to give time for bgg to prepare game list)', '30'),
                                                            ('crons:on', 'If crons are turned on', '1'),
                                                            ('games:news_before', 'All game updates below X days are \"New\"', '7'),
                                                            ('games:played_before', 'All games played below X days are \"Recently played\"', '31'),
                                                            ('img:community_image_path', '', 'community/logo'),
                                                            ('img:game_image_path', '', 'games/images'),
                                                            ('tags:auto_length_tag_type_id', '', '2'),
                                                            ('tags:auto_weight_tag_type_id', '', '1'),
                                                            ('tags:expansion_tag_type_id', '', '9');
SET FOREIGN_KEY_CHECKS=1;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;