ALTER TABLE `permission_roles` CHANGE `subject` `subject` ENUM('global','community','userdata','event') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'global';
ALTER TABLE `permission_user2role` CHANGE `subject` `subject` ENUM('global','community','userdata','event') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'global';

INSERT INTO `permission_roles` (`role`, `title`, `subject`) VALUES ('host', 'Host', 'community'), ('cohost', 'Co-host', 'event'), ('participant', 'Participant', 'event'), ('interested', 'Interested', 'event'), ('requested', 'Requested', 'event'), ('unwelcome', 'Unwelcome', 'event');

ALTER TABLE party.events DROP FOREIGN KEY events_ibfk_1;
ALTER TABLE party.events DROP FOREIGN KEY events_ibfk_2;
ALTER TABLE `events`
DROP `community_id`,
  DROP `creator_id`;

-- phpMyAdmin SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: pr-db-mysql:3306
-- Generation Time: Dec 14, 2024 at 01:52 PM
-- Server version: 9.1.0
-- PHP Version: 8.2.8

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `party`
--

-- --------------------------------------------------------

--
-- Table structure for table `community2event`
--

CREATE TABLE `community2event` (
                                   `community_id` int NOT NULL,
                                   `event_id` int NOT NULL,
                                   `owner` tinyint NOT NULL DEFAULT '1',
                                   `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `community2event`
--
ALTER TABLE `community2event`
    ADD KEY `community_id` (`community_id`),
  ADD KEY `event_id` (`event_id`);

--
-- Constraints for dumped tables
--

--
-- Constraints for table `community2event`
--
ALTER TABLE `community2event`
    ADD CONSTRAINT `community2event_ibfk_1` FOREIGN KEY (`community_id`) REFERENCES `community` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `community2event_ibfk_2` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

ALTER TABLE `community2event` ADD INDEX(`community_id`, `event_id`);

ALTER TABLE `community` ADD `events` ENUM('all','trusted','moders','none') NOT NULL DEFAULT 'moders' AFTER `welcome`;

INSERT INTO `permission_roles` (`role`, `title`, `subject`) VALUES ('trustedmenber', 'Trusted Member', 'community');
INSERT INTO `permission_roles` (`role`, `title`, `subject`) VALUES ('reserved', 'Reserved', 'event');

ALTER TABLE `event2game` ADD `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER `agr_status`;
ALTER TABLE `event2game` CHANGE `added` `added` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;