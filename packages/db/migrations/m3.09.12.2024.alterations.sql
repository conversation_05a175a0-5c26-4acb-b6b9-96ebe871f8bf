ALTER TABLE `users` CHANGE `inactive` `active` TINYINT(1) NOT NULL DEFAULT '1';
ALTER TABLE `community` CHANGE `shareOnPermission` `allow_share` TINYINT(1) NOT NULL DEFAULT '1';
ALTER TABLE `users` ADD `last_play_count` INT NOT NULL DEFAULT '0' AFTER `color`;

ALTER TABLE `user2base_game` CHANGE `last_updated` `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE `user2base_game` CHANGE `last_updated` `last_updated` TIMESTAMP NULL DEFAULT NULL;

ALTER TABLE `user2base_game` ADD `calc_tags` TEXT NOT NULL AFTER `deleted`;

INSERT INTO `crons` (`id`, `last_run`, `is_on`, `refetch`, `run_period`, `is_running`, `blocking`, `items_per_batch`) VALUES ('base_games', CURRENT_TIMESTAMP, '1', '10', '3', '0', '0', '1000');