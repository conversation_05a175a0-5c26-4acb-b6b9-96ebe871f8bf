-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: pr-db-mysql:3306
-- Generation Time: Dec 14, 2024 at 01:36 PM
-- Server version: 9.1.0
-- PHP Version: 8.2.8

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `party`
--

-- --------------------------------------------------------

--
-- Table structure for table `role_settings`
--

CREATE TABLE `role_settings` (
                                 `id` int NOT NULL,
                                 `role_id` varchar(20) COLLATE utf8mb4_general_ci NOT NULL,
                                 `setting_name` enum('create_communities_max') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                 `value` varchar(100) COLLATE utf8mb4_general_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `role_settings`
--

INSERT INTO `role_settings` (`id`, `role_id`, `setting_name`, `value`) VALUES
                                                                           (1, 'user', 'create_communities_max', '2'),
                                                                           (2, 'trusted', 'create_communities_max', '5'),
                                                                           (3, 'unverified', 'create_communities_max', 'false'),
                                                                           (4, 'admin', 'create_communities_max', 'true'),
                                                                           (5, 'superadmin', 'create_communities_max', 'true'),
                                                                           (6, 'banned', 'create_communities_max', 'false');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `role_settings`
--
ALTER TABLE `role_settings`
    ADD PRIMARY KEY (`id`),
  ADD KEY `role_id` (`role_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `role_settings`
--
ALTER TABLE `role_settings`
    MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `role_settings`
--
ALTER TABLE `role_settings`
    ADD CONSTRAINT `role_settings_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `permission_roles` (`role`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;