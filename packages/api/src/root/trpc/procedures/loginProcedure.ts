import { TRPCError } from "@trpc/server"

import { t } from "../trpc"

export const loginProcedure = t.procedure.use(async function isAuthed(opts) {
  /*
  if (process.env.NODE_ENV === "development") {
    return opts.next(await fakeLogin())
  }
   */

  if (!opts.ctx.auth) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Unauthorized: : You are not logged in: no authorization",
    })
  }

  if (!opts.ctx.auth.payload?.sub) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Unauthorized: : You are not logged in: no sub",
    })
  }

  return opts.next({
    ctx: {
      // Infers the `session` as non-nullable
      auth: opts.ctx.auth,
    },
  })
})