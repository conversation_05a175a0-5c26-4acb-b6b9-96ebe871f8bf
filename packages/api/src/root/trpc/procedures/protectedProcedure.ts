import { TRPCError } from "@trpc/server"

import { checkLogin } from "../checkLogin"
import { t } from "../trpc"

export const protectedProcedure = t.procedure.use(
  async function isAuthed(opts) {
    /*
    if (process.env.NODE_ENV === "development") {
      return opts.next(await fakeLogin())
    }

     */

    if (!opts.ctx.auth) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : You are not logged in",
      })
    }

    const loginData = await checkLogin(opts.ctx.auth.payload?.sub)

    return opts.next({
      ctx: {
        // Infers the `session` as non-nullable
        auth: opts.ctx.auth,
        loginData,
      },
    })
  },
)