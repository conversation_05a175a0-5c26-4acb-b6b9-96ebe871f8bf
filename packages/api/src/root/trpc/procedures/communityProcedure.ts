import { TRPCError } from "@trpc/server"
import { eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../common/src/permissions/hasPermissions"
import { db } from "../../db"
import { communitySchema } from "../../db/schema/community.schema"
import { checkLogin } from "../checkLogin"
import { t } from "../trpc"

export const communityProcedure = t.procedure
  .input(z.object({ communityId: z.number() }))
  .use(async function isAuthed(opts) {
    /*
    if (process.env.NODE_ENV === "development") {
      return opts.next(await fakeLogin())
   }
     */

    if (!opts.ctx.auth) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : You are not logged in",
      })
    }
    const loginData = await checkLogin(opts.ctx?.auth?.payload?.sub)

    const { communityId } = opts.input

    if (!communityId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : No Community Selected",
      })
    }

    const community = await db
      .select({
        id: communitySchema.id,
        openness: communitySchema.openness,
        events: communitySchema.events,
      })
      .from(communitySchema)
      .where(eq(communitySchema.id, communityId))
      .then((data) => data?.[0])

    if (!community) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : Such community does not exist",
      })
    }

    if (
      !hasPermission(loginData, "community", "view", community) &&
      !hasPermission(loginData, "community", "viewGeneral", community)
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "Forbidden: : You have no access to this community",
      })
    }

    return opts.next({
      ctx: {
        // Infers the `session` as non-nullable
        auth: opts.ctx.auth,
        loginData,
        community,
      },
    })
  })
