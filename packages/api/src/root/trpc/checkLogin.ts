import { TRPCError } from "@trpc/server"
import { eq } from "drizzle-orm"

import { db } from "../db"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { roleSettingsSchema } from "../db/schema/roleSettings.schema"
import { usersSchema } from "../db/schema/users.schema"

export interface Roles {
  role: string
  subject: "global" | "community" | "userdata" | "event"
  subjectId: number | null
}

export const checkLogin = async (
  sub?: string,
): Promise<{ id: number; roles: Roles[] }> => {
  if (!sub) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Unauthorized: : You are not logged in",
    })
  }

  const loginData = await db
    .select({ id: usersSchema.id })
    .from(usersSchema)
    .where(eq(usersSchema.authId, sub))

  if (!loginData) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Unauthorized: : No such user exists!",
    })
  }

  const roles = await db
    .select({
      role: permissionUserToRoleSchema.roleId,
      subject: permissionUserToRoleSchema.subject,
      subjectId: permissionUserToRoleSchema.subjectId,
    })
    .from(permissionUserToRoleSchema)
    .where(eq(permissionUserToRoleSchema.userId, loginData[0].id))
    .then(async (roles) => {
      return await Promise.all(
        roles.map(async (role) => {
          const roleSettings = await db
            .select({
              name: roleSettingsSchema.name,
              value: roleSettingsSchema.value,
            })
            .from(roleSettingsSchema)
            .where(eq(roleSettingsSchema.roleId, role.role))
            .then((settings) => settings)

          return { ...role, roleSettings }
        }),
      )
    })

  return { ...loginData[0], roles }
}
