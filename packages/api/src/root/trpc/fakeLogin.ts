import { TRPCError } from "@trpc/server"
import { eq } from "drizzle-orm"

import { db } from "../db"
import { communitySchema } from "../db/schema/community.schema"
import { communityToEventSchema } from "../db/schema/communityToEvent.schema"
import { eventSchema } from "../db/schema/event.schema"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { roleSettingsSchema } from "../db/schema/roleSettings.schema"

export const fakeLogin = async ({
  communityId,
  eventId,
}: {
  communityId?: number
  eventId?: number
}) => {
  return {
    ctx: {
      // Infers the `session` as non-nullable
      auth: {
        payload: {
          sub: "google-oauth2|110962012279882593616",
        },
      },
      community: communityId
        ? await db
            .select({
              id: communitySchema.id,
              openness: communitySchema.openness,
              events: communitySchema.events,
            })
            .from(communitySchema)
            .where(eq(communitySchema.id, communityId))
            .then((data) => data?.[0])
        : undefined,
      event: eventId
        ? await db
            .select({
              id: eventSchema.id,
              openness: eventSchema.openness,
              status: eventSchema.state,
            })
            .from(eventSchema)
            .where(eq(eventSchema.id, eventId))
            .then(async (event) => {
              if (!event[0]) {
                throw new TRPCError({
                  code: "NOT_FOUND",
                  message: "Unauthorized: : Such event does not exist",
                })
              }

              const communities = await db
                .select({
                  id: communityToEventSchema.communityId,
                  owner: communityToEventSchema.owner,
                })
                .from(communityToEventSchema)
                .where(eq(communityToEventSchema.eventId, event[0].id))
                .then((communities) => communities)

              return { ...event[0], communities }
            })
        : undefined,
      loginData: {
        id: 1,
        roles: await db
          .select({
            role: permissionUserToRoleSchema.roleId,
            subject: permissionUserToRoleSchema.subject,
            subjectId: permissionUserToRoleSchema.subjectId,
          })
          .from(permissionUserToRoleSchema)
          .where(eq(permissionUserToRoleSchema.userId, 1))
          .then(async (roles) => {
            return await Promise.all(
              roles.map(async (role) => {
                const roleSettings = await db
                  .select({
                    name: roleSettingsSchema.name,
                    value: roleSettingsSchema.value,
                  })
                  .from(roleSettingsSchema)
                  .where(eq(roleSettingsSchema.roleId, role.role))
                  .then((settings) => settings)

                return { ...role, roleSettings }
              }),
            )
          }),
      },
    },
  }
}
