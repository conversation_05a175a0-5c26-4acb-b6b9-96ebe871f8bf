import { initTRPC } from "@trpc/server"
import * as trpcExpress from "@trpc/server/adapters/express"

export type AuthResult =
  | {
      payload: {
        sub: string
        name?: string
        email?: string
        email_verified?: boolean
        picture?: string
        user_id: string
      }
    }
  | undefined

export const createContext = ({
  res,
}: trpcExpress.CreateExpressContextOptions): {
  auth: AuthResult
} => {
  const auth = res.locals.auth
  return {
    auth,
  }
}

export type Context = Awaited<ReturnType<typeof createContext>>

export const t = initTRPC.context<Context>().create()

export const router = t.router
