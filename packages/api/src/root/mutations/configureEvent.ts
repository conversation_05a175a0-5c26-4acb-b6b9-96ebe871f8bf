import { TRPCError } from "@trpc/server"
import dayjs from "dayjs"
import utc from "dayjs/plugin/utc"
import { eq } from "drizzle-orm"

import { createEventInputs } from "../../../../common/src/zodSchemas/postSchemas"
import { db } from "../db"
import { eventSchema } from "../db/schema/event.schema"
import { hasPermission } from "../permissions"
import { eventProcedure } from "../trpc/procedures/eventProcedure"

export const configureEvent = eventProcedure
  .input(createEventInputs)
  .mutation(async ({ input, ctx: { loginData } }) => {
    if (!input.id) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "No event ID",
      })
    }

    if (!hasPermission(loginData, "event", "update", { id: input.id })) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't create new event",
      })
    }

    dayjs.extend(utc)

    await db
      .update(eventSchema)
      .set({
        title: input.title,
        location: input.location ?? null,
        openness: input.openness,
        memberApproval: input.approval ?? true,
        description: input.description ?? null,
        starts: dayjs(input.starts).utc().toDate(),
        ends: input.ends ? dayjs(input.ends).toDate() : null,
        maxCapacity: input.maxCapacity ?? null,
        reserveCapacity: input.reserveCapacity ?? null,
        state: input.state ?? "hidden",
        share: input.share ?? true,
        hasAgeLimit: input.hasAgeLimit ?? false,
        minCapacity: input.minCapacity ?? null,
        smallDescription: input.smallDescription ?? null,
        lat: input.lat ?? null,
        lng: input.lng ?? null,
      })
      .where(eq(eventSchema.id, input.id))
      .then((re) => re[0])

    return { success: true, eventId: input.id }
  })
