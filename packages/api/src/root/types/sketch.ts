// data for all games
interface Request {
  games: Game[]
  users: UserData[]
  tags: TagData[] // as is, no changes
  tagsCategories: TagCategoryData[] // as is, no changes
}

interface Game {
  // g2c2: news: string // this contains info about last significant interaction instead of last_updated
  // games: average: number // float, average of base game
  // games: bggId: number // base game
  // games: id: number
  // games: title: string // base game
  // g2c2 a: tags: number[] // list of id's for all tags for base and expansions sans expansion-specific tags
  // g2c2 a: users: User[] // all users of base and expansions
  // g2c2 a: expansions: string[] // list of all expansion titles for search
  // games : players: Players // calculate taking into account expansion info
}

interface User {
  // users g2c2: id: number
  // users g2c2: experience: number // expert rating, see Jira task
}

interface UserData {
  // user2community
  // users: name: number
  // users: id: number
  // users: avatar: string
  // users: color: string
}

// data for single game

interface GRequest {
  game: GameData
  user: UserDataExtended[] // all users that can be
  tags: TagData[] // all tags that can be, data as is in "Request"
  expansions: Expansion[]
}

interface GameData {
  // g2c2: news: string // this contains info about last significant interaction instead of last_updated
  // games: average: number // float, average of base game
  // games: bggId: number // base game
  // games: id: number
  // games: title: string // base game
  // g2c2 a: players: Players // calculate taking into account expansion info
  // g2c2 a: length: Length // as is
  // g2c2 a: age: number
}

interface UserDataExtended {
  // users: id: number
  // users: display_name: string
  // u2bg: experience: number // expert rating, see Jira task
  // u2bg: can_teach: boolean // replace knows_rules
  // u2bg: last_play: string
  // u2bg: play_count: number
  // u2bg: rating: number
  // users: color: string // avatar OR color, don't send both!
  // users: avatar: string
  // u2bg: portability: string
  // u2bg: events: string // show and use only when events are added
  // u2bg: play_priority: string // "anytime", "often", "on-request", "on-plead", "never", "please-burn-the-game"
}

interface Expansion {
  // i2c: id: number
  // i2c: title: string
  // i2c: bggRating: number
  // i2c a: users: number[]
  // i2c a: expansionTags: number[]
  // i2c: lastUpdated: boolean
  // i2c: type: string
}
