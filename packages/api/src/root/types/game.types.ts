export type Relationship =
  | "owner"
  | "moder"
  | "user"
  | "guest"
  | "invited"
  | "banned"

export type Portability = null | "home" | "anywhere" | "onrequest" | "nowhere"
export type Events = null | "willbring" | "canask" | "willnotbring"

export interface GameDataUser {
  id: number
  name: string
  last_play: string
  knows_rules: number
  play_count: number
  rating: number
  color: string
  avatar: string
  portability?: Portability
  events?: Events
}

export type Tags = number[]

export type GameUsersShort = [number, number]

export interface GameDataExtracted {
  users?: GameDataUser[]
  tags?: Tags
}

export type PlayTimeData = {
  box: {
    max?: string
    min?: string
  }
}

export interface PlayerCountStatsRow {
  players: string
  votes: string
  status: number
}

export interface PlayersCountData {
  box: {
    max?: string
    min?: string
  }
  stats?: PlayerCountStatsRow[]
}

export interface BggGameDataExtracted {
  age: string
  average: string
  averageweight: string
  bayesaverage: string
  publishYear: string
  rank: string
  players: PlayersCountData
  playTime: PlayTimeData
}
