import { eq, like } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../db"
import { communitySchema } from "../../db/schema/community.schema"
import { userToCommunitySchema } from "../../db/schema/userToCommunity.schema"
import { protectedProcedure } from "../../trpc/procedures/protectedProcedure"

import { SEARCH_ITEM_COUNT } from "./searchConfigs"

export const searchCommunities = protectedProcedure
  .input(z.object({ search: z.string() }))
  .query(async ({ input }) => {
    const communities = await db
      .select({
        id: communitySchema.id,
        name: communitySchema.name,
        image: communitySchema.image,
      })
      .from(communitySchema)
      .innerJoin(
        communitySchema,
        eq(communitySchema.id, userToCommunitySchema.communityId),
      )
      .where(
        like(communitySchema.name, `%${input.search.trim().substring(0, 20)}%`),
      )
      .limit(SEARCH_ITEM_COUNT)

    return communities
  })
