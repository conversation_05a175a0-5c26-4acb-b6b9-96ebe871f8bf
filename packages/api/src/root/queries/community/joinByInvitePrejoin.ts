import { TRPCError } from "@trpc/server"
import { and, eq, gte } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../db"
import { communitySchema } from "../../db/schema/community.schema"
import { communityInvitesSchema } from "../../db/schema/communityInvites.schema"
import { userToCommunitySchema } from "../../db/schema/userToCommunity.schema"
import { protectedProcedure } from "../../trpc/procedures/protectedProcedure"

export const joinByInvitePrejoin = protectedProcedure
  .input(
    z.object({
      code: z.string(),
    }),
  )
  .query(async ({ input, ctx: { loginData } }) => {
    const invite = await db
      .select({
        id: communityInvitesSchema.id,
        expiration: communityInvitesSchema.expiration,
        communityId: communityInvitesSchema.communityId,
        status: communityInvitesSchema.status,
        acceptLimit: communityInvitesSchema.acceptLimit,
        count: communityInvitesSchema.count,
        accepted: communityInvitesSchema.accepted,
      })
      .from(communityInvitesSchema)
      .where(
        and(
          gte(communityInvitesSchema.expiration, new Date()),
          eq(communityInvitesSchema.inviteString, input.code ?? ""),
        ),
      )
      .then((invite) => invite[0])

    if (!invite) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "Invite not found!",
      })
    }

    if (
      invite.status !== "sent" ||
      (invite.acceptLimit !== null && invite.acceptLimit <= (invite.count ?? 0))
    ) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "Valid invite required to join this community",
      })
    }

    const connectionCheck = await db
      .select({ communityId: userToCommunitySchema.communityId })
      .from(userToCommunitySchema)
      .where(
        and(
          eq(userToCommunitySchema.userId, loginData.id),
          eq(userToCommunitySchema.communityId, invite.communityId),
        ),
      )
      .then((communityId) => communityId[0])

    if (connectionCheck) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "User already has joined this community",
      })
    }

    const community = await db
      .select({
        id: communitySchema.id,
        approval: communitySchema.memberApproval,
        share: communitySchema.allowShare,
        image: communitySchema.image,
        openness: communitySchema.openness,
        location: communitySchema.location,
        name: communitySchema.name,
        online: communitySchema.online,
      })
      .from(communitySchema)
      .where(eq(communitySchema.id, invite.communityId))
      .then((community) => community[0])

    return { community }
  })
