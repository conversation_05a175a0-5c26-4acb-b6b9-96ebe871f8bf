import { eq } from "drizzle-orm"

import { db } from "../../db"
import { getCommunityDetails } from "../../db/queries/getCommunityDetails"
import { communitySchema } from "../../db/schema/community.schema"
import { userToCommunitySchema } from "../../db/schema/userToCommunity.schema"
import { selectCommunityData } from "../../db/select/select.commuityData"
import { protectedProcedure } from "../../trpc/procedures/protectedProcedure"

export const communityList = protectedProcedure.query(
  async ({ ctx: { loginData } }) => {
    // const imagePath = await communityImagePath()

    return await db
      .select(selectCommunityData)
      .from(userToCommunitySchema)
      .innerJoin(
        communitySchema,
        eq(communitySchema.id, userToCommunitySchema.communityId),
      )
      .where(eq(userToCommunitySchema.userId, loginData.id))
      .then(async (communities) => {
        return Promise.all(
          communities.map(
            async (community) =>
              await getCommunityDetails({ community, loginData }),
          ),
        )
      })
  },
)
