import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../db"
import { getCommunityDetails } from "../../db/queries/getCommunityDetails"
import { communitySchema } from "../../db/schema/community.schema"
import { userToCommunitySchema } from "../../db/schema/userToCommunity.schema"
import { selectCommunityData } from "../../db/select/select.commuityData"
import { hasPermission } from "../../permissions"
import { communityProcedure } from "../../trpc/procedures/communityProcedure"

export const communityBasic = communityProcedure
  .input(z.object({ communityId: z.number() }))
  .query(async ({ input, ctx: { loginData } }) => {
    // const imagePath = await communityImagePath()

    const communityInfo = await db
      .select({
        ...selectCommunityData,
        approval: communitySchema.memberApproval,
        share: communitySchema.allowShare,
        description: communitySchema.description,
        welcome: communitySchema.welcome,
        events: communitySchema.events,
      })
      .from(communitySchema)
      .leftJoin(
        userToCommunitySchema,
        and(
          eq(userToCommunitySchema.communityId, communitySchema.id),
          eq(userToCommunitySchema.userId, loginData.id),
        ),
      )
      .where(eq(communitySchema.id, input.communityId))
      .then(
        async (community) =>
          await getCommunityDetails({ community: community[0], loginData }),
      )

    let canSeeLocation = false

    if (
      hasPermission(loginData, "community", "view", {
        id: communityInfo.id,
        openness: communityInfo.openness,
      })
    ) {
      canSeeLocation = true
    }

    return {
      ...communityInfo,
      location: canSeeLocation ? communityInfo.location : undefined,
      online: canSeeLocation ? communityInfo.online : undefined,
    }
  })
