import { and, desc, eq, inArray, or } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../db"
import { communityToEventSchema } from "../../db/schema/communityToEvent.schema"
import { eventSchema } from "../../db/schema/event.schema"
import { gameToCommunity2Schema } from "../../db/schema/gameToCommunity2.schema"
import { gamesSchema } from "../../db/schema/games.schema"
import { permissionUserToRoleSchema } from "../../db/schema/permissionUserToRole.schema"
import { userToCommunitySchema } from "../../db/schema/userToCommunity.schema"
import { usersSchema } from "../../db/schema/users.schema"
import { selectGameData } from "../../db/select/select.gameData"
import { communityProcedure } from "../../trpc/procedures/communityProcedure"
import { extractGameData, stripExtracted } from "../helpers/extractGameData"

const COMMUNITY_HOME_GAME_COUNT = 20
const COMMUNITY_HOME_USER_COUNT = 20
const COMMUNITY_HOME_EVENT_COUNT = 20

export const communityExpanded2 = communityProcedure
  .input(z.object({ communityId: z.number() }))
  .query(async ({ input, ctx: { loginData } }) => {
    const games = await db
      .select(selectGameData)
      .from(gameToCommunity2Schema)
      .innerJoin(gamesSchema, eq(gameToCommunity2Schema.gameId, gamesSchema.id))
      .where(
        and(
          eq(gameToCommunity2Schema.communityId, input.communityId),
          eq(gamesSchema.type, "base"),
        ),
      )
      .orderBy(desc(gameToCommunity2Schema.news))
      .limit(COMMUNITY_HOME_GAME_COUNT)
      .then(async (games) => {
        let uniqueUsers: number[] = []
        const gamesUpdated = games.map((game) => {
          const { bggDataExtracted, users } = extractGameData(game)

          uniqueUsers = uniqueUsers.concat(
            users
              .map((user) => user[0])
              .filter((user) => uniqueUsers.indexOf(user) === -1),
          )

          return {
            ...game,
            ...stripExtracted,
            users,
            average: Math.round(parseFloat(bggDataExtracted.average) * 10) / 10,
          }
        })

        const users = await db
          .select({
            name: usersSchema.name,
            id: usersSchema.id,
            avatar: usersSchema.avatar,
            color: usersSchema.color,
          })
          .from(usersSchema)
          .where(inArray(usersSchema.id, uniqueUsers))

        return { games: gamesUpdated, users }
      })

    const users = await db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        email: usersSchema.email,
        bggUserName: usersSchema.bggUsername,
        color: usersSchema.color,
        gameCount: usersSchema.gameCount,
        avatar: usersSchema.avatar,
      })
      .from(usersSchema)
      .innerJoin(
        userToCommunitySchema,
        eq(usersSchema.id, userToCommunitySchema.userId),
      )
      .where(eq(userToCommunitySchema.communityId, input.communityId))
      .orderBy(desc(userToCommunitySchema.created))
      .limit(COMMUNITY_HOME_USER_COUNT)
      .then((users) => {
        return users
      })

    const events = await db
      .select({
        id: eventSchema.id,
        title: eventSchema.title,
        starts: eventSchema.starts,
        role: permissionUserToRoleSchema.roleId,
      })
      .from(communityToEventSchema)
      .innerJoin(
        eventSchema,
        eq(communityToEventSchema.eventId, eventSchema.id),
      )
      .leftJoin(
        permissionUserToRoleSchema,
        and(
          eq(permissionUserToRoleSchema.subjectId, eventSchema.id),
          eq(permissionUserToRoleSchema.subject, "event"),
          eq(permissionUserToRoleSchema.userId, loginData.id),
        ),
      )
      .where(
        and(
          eq(communityToEventSchema.communityId, input.communityId),
          or(eq(eventSchema.state, "open"), eq(eventSchema.state, "ongoing")),
        ),
      )
      .limit(COMMUNITY_HOME_EVENT_COUNT)
      .then((events) => {
        return events
      })

    return {
      games,
      users,
      events,
    }
  })
