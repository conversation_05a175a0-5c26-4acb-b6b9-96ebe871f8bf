import { TRPCError } from "@trpc/server"
import { and, eq, inArray } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../db"
import { permissionUserToRoleSchema } from "../../db/schema/permissionUserToRole.schema"
import { usersSchema } from "../../db/schema/users.schema"
import { Role, hasPermission } from "../../permissions"
import { eventProcedure } from "../../trpc/procedures/eventProcedure"

export const eventParticipant = eventProcedure
  .input(z.object({ eventId: z.number(), participantId: z.number() }))
  .query(async ({ input, ctx: { loginData } }) => {
    if (
      !hasPermission(loginData, "event", "view", {
        id: input.eventId,
      })
    ) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : You cannot see this data",
      })
    }

    const canSee: Role[] = ["interested", "participant"]

    if (
      hasPermission(loginData, "event", "approve", {
        id: input.eventId,
      })
    ) {
      canSee.push("unwelcome")
      canSee.push("requested")
      canSee.push("reserved")
    }

    return db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        bggUsername: usersSchema.bggUsername,
        color: usersSchema.color,
        avatar: usersSchema.avatar,
      })
      .from(usersSchema)
      .innerJoin(
        permissionUserToRoleSchema,
        eq(permissionUserToRoleSchema.userId, usersSchema.id),
      )
      .where(
        and(
          eq(usersSchema.id, input.participantId),
          eq(permissionUserToRoleSchema.subject, "event"),
          inArray(permissionUserToRoleSchema.roleId, canSee),
          eq(permissionUserToRoleSchema.subjectId, input.eventId ?? 0),
        ),
      )
      .then(async (userResponse) => {
        const user = userResponse[0]
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          })
        }

        const bggUsername = user.bggUsername

        const roles = await db
          .select({
            role: permissionUserToRoleSchema.roleId,
            subject: permissionUserToRoleSchema.subject,
            subjectId: permissionUserToRoleSchema.subjectId,
          })
          .from(permissionUserToRoleSchema)
          .where(
            and(
              eq(permissionUserToRoleSchema.userId, user.id),
              eq(permissionUserToRoleSchema.subject, "event"),
              eq(permissionUserToRoleSchema.subjectId, input.eventId ?? 0),
            ),
          )
          .then((roles) => roles)

        return { ...user, bggUsername, roles }
      })
  })
