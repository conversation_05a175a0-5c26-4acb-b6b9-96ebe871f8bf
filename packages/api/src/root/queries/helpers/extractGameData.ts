import { BggGameDataExtracted, GameUsersShort } from "../../types/game.types"

interface GameData {
  users?: string | null
  tags?: string | null
  bggInfo?: string | null
  expansions?: string | null
}
export const extractGameData = (game: GameData) => {
  const users: GameUsersShort[] = JSON.parse(
    game.users ?? "[]",
  ) as GameUsersShort[]

  const tags: number[] = JSON.parse(game.tags ?? "[]") as number[]

  const bggDataExtracted: BggGameDataExtracted = JSON.parse(
    game.bggInfo ?? "{}",
  ) as BggGameDataExtracted

  const expansions: string[] = JSON.parse(game.expansions ?? "[]") as string[]

  return {
    users,
    tags,
    bggDataExtracted,
    expansions,
  }
}

export const stripExtracted = {
  users: undefined,
  tags: undefined,
  bggInfo: undefined,
  expansions: undefined,
}
