import { eq } from "drizzle-orm"

import { db } from "../db"
import { tagCategoriesSchema } from "../db/schema/tagCategories.schema"
import { tagsSchema } from "../db/schema/tags.schema"
import { publicProcedure } from "../trpc/procedures/publicProcedure"

export const tagList = publicProcedure.query(async () => {
  const tags = await db
    .select({
      id: tagsSchema.id,
      title: tagsSchema.title,
      type: tagsSchema.type,
    })
    .from(tagsSchema)
    .where(eq(tagsSchema.visible, true))
    .then((tags) => {
      return tags
    })

  const tagCategories = await db
    .select({
      id: tagCategoriesSchema.id,
      title: tagCategoriesSchema.title,
      color: tagCategoriesSchema.color,
    })
    .from(tagCategoriesSchema)
    .then((tagCats) => {
      return tagCats
    })

  return {
    tags,
    tagCategories,
  }
})
