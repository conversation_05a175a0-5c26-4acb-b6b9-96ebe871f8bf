import { eq } from "drizzle-orm"

import { db } from "../index"
import { tagCategoriesSchema } from "../schema/tagCategories.schema"
import { tagsSchema } from "../schema/tags.schema"

export const tagsAndCats = async () => {
  const tags = await db
    .select({
      id: tagsSchema.id,
      title: tagsSchema.title,
      type: tagsSchema.type,
    })
    .from(tagsSchema)
    .where(eq(tagsSchema.visible, true))
    .then((tags) => {
      return tags
    })

  const tagCategories = await db
    .select({
      id: tagCategoriesSchema.id,
      title: tagCategoriesSchema.title,
      color: tagCategoriesSchema.color,
    })
    .from(tagCategoriesSchema)
    .then((tagCats) => {
      return tagCats
    })

  return { tags, tagCategories }
}
