import { eq } from "drizzle-orm"

import { db } from "../index"
import { permissionUserToRoleSchema } from "../schema/permissionUserToRole.schema"

import { getUserRoleSettings } from "./getUserRoleSettings"

export const getUserRoles = async (userId: number) => {
  return await db
    .select({
      role: permissionUserToRoleSchema.roleId,
      subject: permissionUserToRoleSchema.subject,
      subjectId: permissionUserToRoleSchema.subjectId,
    })
    .from(permissionUserToRoleSchema)
    .where(eq(permissionUserToRoleSchema.userId, userId))
    .then(
      async (roles) =>
        await Promise.all(
          roles.map(async (role) => {
            const roleSettings = await getUserRoleSettings(role.role)
            return { ...role, roleSettings }
          }),
        ),
    )
}
