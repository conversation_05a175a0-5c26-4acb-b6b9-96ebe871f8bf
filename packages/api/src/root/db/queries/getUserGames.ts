import { and, eq, isNotNull } from "drizzle-orm"
import { undefined } from "zod"

import { BggGameDataExtracted } from "../../types/game.types"
import { db } from "../index"
import { gamesSchema } from "../schema/games.schema"
import { userToBaseGameSchema } from "../schema/userToBaseGame.schema"

export const getUserGames = async (userId: number) => {
  return await db
    .select({
      id: gamesSchema.id,
      title: gamesSchema.title,
      bggId: gamesSchema.bggId,
      rating: userToBaseGameSchema.rating,
      lastPlay: userToBaseGameSchema.lastPlay,
      news: userToBaseGameSchema.news,
      playCount: userToBaseGameSchema.playCount,
      bggInfo: gamesSchema.bggInfo,
      lastUpdated: userToBaseGameSchema.lastUpdated,
      tags: userToBaseGameSchema.tags,
    })
    .from(userToBaseGameSchema)
    .innerJoin(gamesSchema, eq(userToBaseGameSchema.gameId, gamesSchema.id))
    .where(
      and(
        eq(userToBaseGameSchema.userId, userId),
        isNotNull(gamesSchema.bggInfo),
      ),
    )
    .then((games) => {
      return games.map((game) => {
        let bggDataExtracted = {} as unknown as BggGameDataExtracted
        try {
          bggDataExtracted = JSON.parse(
            game.bggInfo ?? "{}",
          ) as BggGameDataExtracted
        } catch (e: unknown) {
          console.error(
            "Error while parsing bgg info:",
            e,
            `Info: ${game.bggInfo}`,
            `Game id: ${game.id}`,
          )
        }

        let tags: number[] = []

        try {
          tags =
            game.tags && game.tags.length > 0
              ? (JSON.parse(game.tags ?? "[]") as number[])
              : []
        } catch (e: unknown) {
          console.error(
            "Error while parsing tags:",
            e,
            `Tags: ${game.tags}`,
            `Game id: ${game.id}`,
          )
        }

        return {
          ...game,
          tags,
          bggInfo: undefined,
          players: {
            box: {
              min: parseInt(bggDataExtracted.players?.box?.min ?? "0"),
              max: parseInt(bggDataExtracted.players?.box?.max ?? "0"),
            },
            stats: bggDataExtracted.players?.stats?.map((stat) => [
              parseInt(stat?.players ?? "0"),
              stat?.status ?? 1,
            ]),
          },
          weight: parseInt(bggDataExtracted.averageweight ?? "0"),
          average:
            Math.round(parseFloat(bggDataExtracted.average ?? "0") * 100) / 100,
        }
      })
    })
}
