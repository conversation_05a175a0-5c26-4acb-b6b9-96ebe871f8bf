import { eq } from "drizzle-orm"

import { db } from "../index"
import { roleSettingsSchema } from "../schema/roleSettings.schema"

export const getUserRoleSettings = async (role: string) => {
  return await db
    .select({
      name: roleSettingsSchema.name,
      value: roleSettingsSchema.value,
    })
    .from(roleSettingsSchema)
    .where(eq(roleSettingsSchema.roleId, role))
    .then((settings) => settings)
}