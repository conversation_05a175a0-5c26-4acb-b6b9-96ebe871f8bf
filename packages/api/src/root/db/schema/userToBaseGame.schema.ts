import {
  boolean,
  int,
  mysqlEnum,
  mysqlTable,
  text,
  timestamp,
} from "drizzle-orm/mysql-core"

import { gamesSchema } from "./games.schema"
import { usersSchema } from "./users.schema"

export const userToBaseGameSchema = mysqlTable("user2base_game", {
  gameId: int("game_id")
    .notNull()
    .references(() => gamesSchema.id),
  userId: int("user_id")
    .notNull()
    .references(() => usersSchema.id),
  lastUpdated: timestamp("last_updated"),
  news: timestamp("last_updated"),
  experience: int("experience"),
  lastPlay: timestamp("last_play"),
  rating: int("rating"),
  portability: mysqlEnum(["home", "anywhere", "onrequest", "nowhere"]),
  events: mysqlEnum(["willbring", "canask", "willnotbring"]),
  willTeach: boolean("will_teach"),
  playCount: int("play_count"),
  tags: text("calc_tags"),
  playPriority: mysqlEnum("play_priority", [
    "anytime",
    "often",
    "on-request",
    "on-plead",
    "never",
    "please-burn-the-game",
  ]),
  deleted: boolean("deleted"),
})
