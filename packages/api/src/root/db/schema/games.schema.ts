import {
  date,
  int,
  mysqlEnum,
  mysqlTable,
  text,
  varchar,
} from "drizzle-orm/mysql-core"

import { commonCreated, commonId } from "./common"

export const gamesSchema = mysqlTable("games", {
  title: varchar({ length: 255 }).notNull(),
  bggId: int("bgg_id").notNull(),
  bggInfo: text("bgg_info"),
  type: mysqlEnum([
    "base",
    "base-expansion",
    "expansion",
    "accessory",
    "other",
  ]).notNull(),
  lastUpdated: date("last_updated").notNull(),
  description: text("description"),
  bggData: text("bgg_data"),
  ...commonId,
  ...commonCreated,
})
