import { int, mysqlTable, text, timestamp } from "drizzle-orm/mysql-core"

import { commonId } from "./common"
import { eventSchema } from "./event.schema"
import { gamesSchema } from "./games.schema"

export const eventToGameSchema = mysqlTable("event2game", {
  ...commonId,
  gameId: int("game_id")
    .notNull()
    .references(() => gamesSchema.id),
  eventId: int("event_id")
    .notNull()
    .references(() => eventSchema.id),
  lastUpdated: timestamp("last_updated"),
  added: timestamp("added"),
  users: text("agr_users"),
  status: text("agr_status"),
  tags: text("agr_tags"),
})
