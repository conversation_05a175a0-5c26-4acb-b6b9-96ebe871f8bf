import {
  boolean,
  int,
  mysqlEnum,
  mysqlTable,
  timestamp,
} from "drizzle-orm/mysql-core"

import { gamesSchema } from "./games.schema"
import { usersSchema } from "./users.schema"

export const userToGameSchema = mysqlTable("user2game", {
  id: int("id"),
  userId: int("user_id")
    .notNull()
    .references(() => usersSchema.id),
  gameId: int("game_id")
    .notNull()
    .references(() => gamesSchema.id),
  lastPlayed: timestamp("last_play"),
  playCount: int("play_count"),
  rating: int("rating"),
  knowsRules: boolean("knows_rules"),
  portability: mysqlEnum(["home", "anywhere", "onrequest", "nowhere"]),
  events: mysqlEnum(["willbring", "canask", "willnotbring"]),
  deleted: boolean("deleted"),
})
