import { int, mysqlTable, text, timestamp } from "drizzle-orm/mysql-core"

import { communitySchema } from "./community.schema"
import { gamesSchema } from "./games.schema"

export const itemToCommunitySchema = mysqlTable("agr_item2community", {
  gameId: int("game_id")
    .notNull()
    .references(() => gamesSchema.id),
  communityId: int("community_id")
    .notNull()
    .references(() => communitySchema.id),
  userCount: int("user_count").notNull(),
  lastUpdated: timestamp("last_updated"),
  news: timestamp(),
  users: text("agr_users"),
  tags: text("agr_expansion_tags"),
})
