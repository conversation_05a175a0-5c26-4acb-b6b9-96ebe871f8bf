import { boolean, int, mysqlTable } from "drizzle-orm/mysql-core"

import { commonCreated } from "./common"
import { communitySchema } from "./community.schema"
import { usersSchema } from "./users.schema"

export const userToCommunitySchema = mysqlTable("user2community", {
  userId: int("user_id")
    .notNull()
    .references(() => usersSchema.id),
  communityId: int("community_id")
    .notNull()
    .references(() => communitySchema.id),
  trust: int().notNull(),
  shareMyGames: boolean("shareMyGames"),
  ...commonCreated,
})
