```
## Summary
This code snippet defines two objects, `commonCreated` and `commonId`, which are used to standardize database schema fields for creation timestamps and primary keys, respectively.

## Example Usage
```javascript
import { commonCreated, commonId } from './path/to/common';

const userSchema = {
  ...commonId,
  ...commonCreated,
  username: string().notNull(),
  email: string().notNull().unique(),
};
```

## Code Analysis
### Inputs
- None directly, but it uses `int` and `timestamp` from the `drizzle-orm/mysql-core` package.
___
### Flow
1. The `timestamp` function is used to create a `created` field with a default value of the current timestamp and ensures it is not null.
2. The `int` function is used to create an `id` field that serves as a primary key and auto-increments.
___
### Outputs
- `commonCreated`: An object with a `created` field configured as a non-null timestamp with a default value.
- `commonId`: An object with an `id` field configured as an auto-incrementing primary key.
___

```