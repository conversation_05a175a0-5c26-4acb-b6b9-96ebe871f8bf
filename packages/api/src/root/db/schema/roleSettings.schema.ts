import { int, mysqlEnum, mysqlTable, varchar } from "drizzle-orm/mysql-core"

import { gamesSchema } from "./games.schema"

export const roleSettingsSchema = mysqlTable("role_settings", {
  id: int("id").primaryKey().autoincrement(),
  roleId: varchar("role_id", { length: 20 })
    .notNull()
    .references(() => gamesSchema.id)
    .notNull(),
  name: mysqlEnum("setting_name", ["create_communities_max"]).notNull(),
  value: varchar("value", { length: 100 }).notNull(),
})
