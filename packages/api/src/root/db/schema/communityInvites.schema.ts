import {
  int,
  mysqlEnum,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core"

import { communitySchema } from "./community.schema"
import { usersSchema } from "./users.schema"

export const communityInvitesSchema = mysqlTable("community_invites", {
  id: int(),
  userId: int("user_id").references(() => usersSchema.id),
  communityId: int("community_id")
    .notNull()
    .references(() => communitySchema.id),
  inviteString: varchar("invite_string", { length: 20 }).notNull(),
  accepted: timestamp("accepted"),
  expiration: timestamp("expiration"),
  status: mysqlEnum(["sent", "used", "expired", "cancelled"]).notNull(),
  acceptLimit: int("accept_limit"),
  count: int("count"),
  inviterId: int("inviter_id").references(() => usersSchema.id),
})
