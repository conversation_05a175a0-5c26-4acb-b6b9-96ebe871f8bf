import { relations } from "drizzle-orm/relations"

import { communitySchema } from "./community.schema"
import { userToCommunitySchema } from "./userToCommunity.schema"
import { usersSchema } from "./users.schema"

export const userRelations = relations(usersSchema, ({ many }) => ({
  community: many(userToCommunitySchema),
  // usersToContracts: many(userToCommunitySchema),
}))

export const communityRelations = relations(communitySchema, ({ many }) => ({
  users: many(userToCommunitySchema),
  // usersToContracts: many(userToCommunitySchema),
}))

export const usersToContractsRelations = relations(
  userToCommunitySchema,
  ({ one }) => ({
    user: one(usersSchema, {
      fields: [userToCommunitySchema.userId],
      references: [usersSchema.id],
    }),
    contract: one(communitySchema, {
      fields: [userToCommunitySchema.communityId],
      references: [communitySchema.id],
    }),
  }),
)
