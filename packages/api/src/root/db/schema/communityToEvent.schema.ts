import { boolean, int, mysqlTable } from "drizzle-orm/mysql-core"

import { commonCreated } from "./common"
import { communitySchema } from "./community.schema"
import { eventSchema } from "./event.schema"

export const communityToEventSchema = mysqlTable("community2event", {
  eventId: int("event_id")
    .notNull()
    .references(() => eventSchema.id),
  communityId: int("community_id")
    .notNull()
    .references(() => communitySchema.id),
  owner: boolean(),
  ...commonCreated,
})
