import { eq } from "drizzle-orm"
import { Request, Response } from "express"

import { hasPermission } from "../../../../common/src/permissions/hasPermissions"
import { db } from "../db"
import { communityToEventSchema } from "../db/schema/communityToEvent.schema"
import { eventSchema } from "../db/schema/event.schema"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { usersSchema } from "../db/schema/users.schema"

import { uploadEventPicture } from "./eventPicture"

export const uploadHandlerEvent = async (req: Request, res: Response) => {
  const authId = res.locals.auth?.payload?.sub

  const eventId: number = parseInt(req.body.eventId)

  if (!eventId) {
    res.status(401).send("No Event ID")
    return
  }

  const event = await db
    .select({ id: eventSchema.id })
    .from(eventSchema)
    .where(eq(eventSchema.id, eventId))
    .then((event) => {
      return event[0]
    })

  if (!event) {
    res.status(401).send("No Event")
    return
  }

  if (!authId) {
    res.status(401).send("Unauthorized!")
    return
  }

  const loginData = authId
    ? await db
        .select({ id: usersSchema.id })
        .from(usersSchema)
        .where(eq(usersSchema.authId, authId))
        .then((users) => {
          return users[0]
        })
    : undefined

  if (!loginData) {
    res.status(401).send("No User")
    return
  }

  if (!req.file) {
    res.status(400).send("No file")
    return
  }

  const roles = await db
    .select({
      role: permissionUserToRoleSchema.roleId,
      subject: permissionUserToRoleSchema.subject,
      subjectId: permissionUserToRoleSchema.subjectId,
    })
    .from(permissionUserToRoleSchema)
    .where(eq(permissionUserToRoleSchema.userId, loginData.id))
    .then((roles) => roles)

  const eventCommunities = await db
    .select({
      id: communityToEventSchema.communityId,
      owner: communityToEventSchema.owner,
    })
    .from(communityToEventSchema)
    .where(eq(communityToEventSchema.eventId, eventId))
    .then((communities) => communities)

  if (
    !hasPermission({ id: loginData.id, roles }, "event", "update", {
      id: eventId,
      hosts: eventCommunities,
    })
  ) {
    res.status(403).send("Forbidden")
    return
  }

  try {
    const fileExtension = req.file.originalname.split(".").pop()
    await uploadEventPicture(req.file.buffer, eventId, fileExtension ?? "jpg")

    await db
      .update(eventSchema)
      .set({
        image: `${eventId}.${fileExtension}?date=${Date.now()}`,
      })
      .where(eq(eventSchema.id, eventId))
  } catch (error: unknown) {
    res.status(500).send(`Error: ${error}`)
  }

  res.status(200).send("OK")
}
