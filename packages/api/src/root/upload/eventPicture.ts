import fs from "node:fs"

import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3"
import dayjs from "dayjs"
import sharp from "sharp"

const largeCoverSize = 1536
const largeCoverHeight = 300
const largeThumbnailSize = 350
const smallThumbnailSize = 100
const quality = {
  jpeg: { quality: 70 },
  webp: { quality: 70 },
  png: { compressionLevel: 7 },
}
export const uploadEventPicture = async (
  file: Buffer,
  eventId: number,
  fileExtension: string,
): Promise<string> => {
  const client = new S3Client({
    region: process.env.AWS_REGION,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID ?? "",
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY ?? "",
    },
  })

  try {
    const sharpImage = sharp(file)
    const metadata = await sharpImage.metadata()

    const { format } = metadata

    let toResize = sharpImage

    if (format && format in quality) {
      switch (format) {
        case "png":
          toResize = sharpImage.png(quality.png)
          break
        case "webp":
          toResize = sharpImage.webp(quality.webp)
          break
        case "jpeg":
          toResize = sharpImage.jpeg(quality.jpeg)
          break
        default:
          toResize = sharpImage
      }
    }

    const temp = "./temp_event"

    if (!fs.existsSync(temp)) {
      fs.mkdirSync(temp)
    }

    const tempPath = `${temp}/${dayjs().format("YYYY-MM-DD")}`
    if (!fs.existsSync(tempPath)) {
      fs.mkdirSync(tempPath)
    }

    const largeCoverFileName = `${tempPath}/cover_${eventId}.${fileExtension}`

    const resizedImageCover = await toResize
      .resize({
        width: largeCoverSize,
        height: largeCoverHeight,
        fit: "cover",
        position: "center",
      })
      .toFile(largeCoverFileName)

    const largeFileName = `${tempPath}/large_${eventId}.${fileExtension}`

    const resizedImageLarge = await toResize
      .resize({
        width: largeThumbnailSize,
        height: largeThumbnailSize,
        fit: "cover",
        position: "center",
      })
      .toFile(largeFileName)

    const smallFileName = `${tempPath}/small_${eventId}.${fileExtension}`
    const resizedImageSmall = await toResize
      .resize({
        width: smallThumbnailSize,
        height: smallThumbnailSize,
        fit: "cover",
        position: "center",
      })
      .toFile(smallFileName)

    const baseName = "event/main/"

    const command = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET ?? "",
      Key: `${baseName}${eventId}.${fileExtension}`,
      Body: file,
      ACL: "public-read",
    })

    await client.send(command)

    const commandLargeCover = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET ?? "",
      Key: `${baseName}largeCover_${eventId}.${fileExtension}`,
      Body: fs.readFileSync(largeCoverFileName),
      ACL: "public-read",
    })

    await client.send(commandLargeCover)

    const commandLarge = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET ?? "",
      Key: `${baseName}large_${eventId}.${fileExtension}`,
      Body: fs.readFileSync(largeFileName),
      ACL: "public-read",
    })

    await client.send(commandLarge)

    const commandSmall = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET ?? "",
      Key: `${baseName}small_${eventId}.${fileExtension}`,
      Body: fs.readFileSync(smallFileName),
      ACL: "public-read",
    })

    await client.send(commandSmall)
  } catch (error) {
    console.error(error)
    throw error
  }

  return "OK"
}
