import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3"

export const defaultCommunityPicture = async (
  file: Buffer,
  communityId: number,
): Promise<string> => {
  const client = new S3Client({
    region: process.env.AWS_REGION,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID ?? "",
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY ?? "",
    },
  })

  const command = new PutObjectCommand({
    Bucket: process.env.S3_BUCKET ?? "",
    Key: `community/logo/${communityId}.png`,
    Body: file,
    ACL: "public-read",
  })

  await client.send(command)

  return "OK"
}
