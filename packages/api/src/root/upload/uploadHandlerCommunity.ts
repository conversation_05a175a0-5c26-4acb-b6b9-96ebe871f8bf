import { eq } from "drizzle-orm"
import { Request, Response } from "express"

import { hasPermission } from "../../../../common/src/permissions/hasPermissions"
import { db } from "../db"
import { communitySchema } from "../db/schema/community.schema"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { usersSchema } from "../db/schema/users.schema"

import { uploadCommunityPicture } from "./communityPicture"
export const uploadHandlerCommunity = async (req: Request, res: Response) => {
  const authId = res.locals.auth?.payload?.sub

  const communityId: number = parseInt(req.body.communityId)

  if (!communityId) {
    res.status(401).send("No Community ID")
    return
  }

  const community = await db
    .select({ id: communitySchema.id })
    .from(communitySchema)
    .where(eq(communitySchema.id, communityId))
    .then((community) => {
      return community[0]
    })

  if (!community) {
    res.status(401).send("No Community")
    return
  }

  if (!authId) {
    res.status(401).send("Unauthorized!")
    return
  }

  const loginData = authId
    ? await db
        .select({ id: usersSchema.id })
        .from(usersSchema)
        .where(eq(usersSchema.authId, authId))
        .then((users) => {
          return users[0]
        })
    : undefined

  if (!loginData) {
    res.status(401).send("No User")
    return
  }

  if (!req.file) {
    res.status(400).send("No file")
    return
  }

  const roles = await db
    .select({
      role: permissionUserToRoleSchema.roleId,
      subject: permissionUserToRoleSchema.subject,
      subjectId: permissionUserToRoleSchema.subjectId,
    })
    .from(permissionUserToRoleSchema)
    .where(eq(permissionUserToRoleSchema.userId, loginData.id))
    .then((roles) => roles)

  if (
    !hasPermission({ id: loginData.id, roles }, "community", "update", {
      id: communityId,
    })
  ) {
    res.status(403).send("Forbidden")
    return
  }

  try {
    const fileExtension = req.file.originalname.split(".").pop()
    await uploadCommunityPicture(
      req.file.buffer,
      communityId,
      fileExtension ?? "jpg",
    )

    await db
      .update(communitySchema)
      .set({
        image: `${communityId}.${fileExtension}?date=${Date.now()}`,
      })
      .where(eq(communitySchema.id, communityId))
  } catch (error: unknown) {
    res.status(500).send(`Error: ${error}`)
  }

  res.status(200).send("OK")
}
