import * as trpcExpress from "@trpc/server/adapters/express"
import bodyParser from "body-parser"
import cors from "cors"
import express from "express"
import multer from "multer"

import { firebaseAuth } from "./firebaseAuth"
import { appRouter } from "./router"
import { createContext } from "./trpc/trpc"
import { uploadHandler } from "./upload/uploadHandler"
import { uploadHandlerCommunity } from "./upload/uploadHandlerCommunity"
import { uploadHandlerEvent } from "./upload/uploadHandlerEvent"

const app = express()

const upload = multer()

app.use(
  cors({
    origin: "*",
  }),
)

// Firebase auth middleware

app.use(firebaseAuth())

app.use("/changeProfile", upload.single("image"), uploadHandler)

app.use("/changeEvent", bodyParser.urlencoded({ extended: true }))
app.use("/changeEvent", upload.single("image"), uploadHandlerEvent)

app.use("/changeCommunity", bodyParser.urlencoded({ extended: true }))
app.use("/changeCommunity", upload.single("image"), uploadHandlerCommunity)

app.use(
  "/trpc",
  trpcExpress.createExpressMiddleware({
    router: appRouter,
    createContext,
  }),
)

app.listen(8888, () => {
  console.log("API: listening on port 8888")
})
