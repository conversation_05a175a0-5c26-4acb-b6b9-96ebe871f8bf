import { defineConfig, mergeConfig } from "vitest/config"

import viteConfig from "./vite.config"

export default mergeConfig(
  viteConfig,
  defineConfig({
    test: {
      environment: "jsdom", // This right here
      globals: true,
      exclude: ["**/node_modules/**", "**/dist/**"],
      coverage: {
        provider: "v8",
        exclude: [
          "**/dist/**",
          "**/node_modules/**",
          "./src/mock/**",
          "./*.config.*",
        ],
      },
    },
  }),
)
