import { z } from "zod";

const MAX_FILE_SIZE = 5000000;
const ACCEPTED_IMAGE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
];

export const uploadImageInput = z.object({
  image: z
    .any()
    .transform((file) => {
      return file.length > 0 && file.item(0);
    })
    .refine((file) => {
      return file?.size <= MAX_FILE_SIZE;
    }, `Max image size is 500kb.`)
    .refine(
      (file) => ACCEPTED_IMAGE_TYPES.includes(file?.type),
      "Only .jpg, .jpeg, .png and .webp formats are supported.",
    ),
});

export type UploadImageInput = z.infer<typeof uploadImageInput>;
