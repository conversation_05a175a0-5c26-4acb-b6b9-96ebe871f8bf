import { type Permissions } from "./objects/general"
import { type Role, type RoleData, type User } from "./roles/helpers/types"
import { RoleProperties } from "./objects/roleProperties"

export type RolePermissionCheckFunctionParams<Key extends keyof Permissions> = {
  user: User
  data: Permissions[Key]["dataType"]
  role?: RoleData
  properties: Partial<RoleProperties> | undefined
}

export type RolePermissionCheckFunction<Key extends keyof Permissions> = (
  props: RolePermissionCheckFunctionParams<Key>,
) => boolean | null

export type PermissionCheck<Key extends keyof Permissions> =
  | boolean
  | RolePermissionCheckFunction<Key>

export type RoleWithPermissions = {
  [Key in keyof Permissions]: Partial<{
    [Action in Permissions[Key]["action"]]: PermissionCheck<Key>
  }>
}

/*
export type RolesWithPermissions = {
  [R in Role]: Partial<RoleWithPermissions>
}
 */

export type RoleWithPermissionsAndProperties = {
  permissions: Partial<RoleWithPermissions>
  properties: Partial<RoleProperties>
}

export type RolesWithPermissions = {
  [R in Role]: Partial<RoleWithPermissionsAndProperties>
}
