import { hasPermission } from "./hasPermissions"
import { User, RoleData, Subject } from "./roles/helpers/types"
import { beforeAll, afterAll, describe, test, expect, vi } from "vitest"
import { roles } from "./roles/general"

// Mock the roles object for testing
vi.mock("./roles/general", () => ({
  roles: {
    testRoleTrue: {
      permissions: {
        testResource: {
          testAction: true,
        },
      },
      properties: {},
    },
    testRoleFalse: {
      permissions: {
        testResource: {
          testAction: false,
        },
      },
      properties: {},
    },
    testRoleNull: {
      permissions: {
        testResource: {
          // testAction is undefined (null)
        },
      },
      properties: {},
    },
    testRoleFunction: {
      permissions: {
        testResource: {
          testAction: ({ user, data }: any) => user.id === data.ownerId,
        },
      },
      properties: {},
    },
    testRoleFunctionFalse: {
      permissions: {
        testResource: {
          testAction: ({}: any) => false,
        },
      },
      properties: {},
    },
  },
}))

// Mock console methods to avoid noise in tests
const originalConsoleInfo = console.info
beforeAll(() => {
  console.info = vi.fn()
})

afterAll(() => {
  console.info = originalConsoleInfo
})

describe("hasPermission - New Logic", () => {
  const createUser = (roles: Partial<RoleData>[]): User => ({
    id: 1,
    roles: roles.map((role) => ({
      role: role.role || "testRoleTrue",
      subject: role.subject || "testResource",
      subjectId: role.subjectId || null,
      ...role,
    })) as RoleData[],
  })

  const testData = { id: 1, ownerId: 1 }

  describe("Basic permission logic", () => {
    test("should grant access when user has role with true permission", () => {
      const user = createUser([{ role: "testRoleTrue" }])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(true)
    })

    test("should deny access when user has role with false permission", () => {
      const user = createUser([{ role: "testRoleFalse" }])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })

    test("should deny access when user has role with null permission", () => {
      const user = createUser([{ role: "testRoleNull" }])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })
  })

  describe("Multiple roles - true overrides null", () => {
    test("should grant access when user has [true, null] permissions", () => {
      const user = createUser([
        { role: "testRoleTrue" },
        { role: "testRoleNull" },
      ])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(true)
    })

    test("should grant access when user has [null, true] permissions", () => {
      const user = createUser([
        { role: "testRoleNull" },
        { role: "testRoleTrue" },
      ])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(true)
    })
  })

  describe("Multiple roles - false overrides true", () => {
    test("should deny access when user has [true, false] permissions", () => {
      const user = createUser([
        { role: "testRoleTrue" },
        { role: "testRoleFalse" },
      ])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })

    test("should deny access when user has [false, true] permissions", () => {
      const user = createUser([
        { role: "testRoleFalse" },
        { role: "testRoleTrue" },
      ])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })
  })

  describe("Multiple roles - false overrides everything", () => {
    test("should deny access when user has [true, false, null] permissions", () => {
      const user = createUser([
        { role: "testRoleTrue" },
        { role: "testRoleFalse" },
        { role: "testRoleNull" },
      ])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })

    test("should deny access when user has [false, null] permissions", () => {
      const user = createUser([
        { role: "testRoleFalse" },
        { role: "testRoleNull" },
      ])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })
  })

  describe("Multiple roles - all null", () => {
    test("should deny access when user has [null, null] permissions", () => {
      const user = createUser([
        { role: "testRoleNull" },
        { role: "testRoleNull" },
      ])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })
  })

  describe("Function-based permissions", () => {
    test("should grant access when function returns true", () => {
      const user = createUser([{ role: "testRoleFunction" }])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(true)
    })

    test("should deny access when function returns false", () => {
      const user = createUser([{ role: "testRoleFunctionFalse" }])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })

    test("should deny access when function returns true but another role has false", () => {
      const user = createUser([
        { role: "testRoleFunction" },
        { role: "testRoleFalse" },
      ])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })

    test("should grant access when function returns true and another role has null", () => {
      const user = createUser([
        { role: "testRoleFunction" },
        { role: "testRoleNull" },
      ])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(true)
    })
  })

  describe("Edge cases", () => {
    test("should deny access when user has no roles", () => {
      const user: User = { id: 1, roles: [] }
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })

    test("should deny access when user has no id", () => {
      const user: User = {
        roles: [
          {
            role: "testRoleTrue",
            subject: "testResource" as Subject,
            subjectId: null,
          },
        ],
      }
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })

    test("should deny access when user has undefined roles", () => {
      const user: User = { id: 1 }
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false)
    })
  })

  describe("Subject and subjectId filtering", () => {
    test("should ignore roles with different subjectId", () => {
      const user = createUser([
        {
          role: "testRoleTrue",
          subject: "testResource" as Subject,
          subjectId: 999,
        }, // Different subjectId
        {
          role: "testRoleFalse",
          subject: "testResource" as Subject,
          subjectId: null,
        }, // Matches
      ])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(false) // Only the false role applies
    })

    test("should include roles with null subjectId", () => {
      const user = createUser([
        {
          role: "testRoleTrue",
          subject: "testResource" as Subject,
          subjectId: null,
        },
      ])
      const result = hasPermission(
        user,
        "testResource" as any,
        "testAction" as any,
        testData,
      )
      expect(result).toBe(true)
    })
  })
})
