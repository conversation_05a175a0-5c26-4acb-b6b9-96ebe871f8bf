import { Permissions } from "./objects/general"
import { RolesWithPermissions } from "./general"
import { roles } from "./roles/general"
import { isRole, Role, User } from "./roles/helpers/types"
import { Community } from "./objects/community"
import { Userdata } from "./objects/userdata"
import { Event } from "./objects/event"
import { GlobalData } from "./objects/global"

const logFilterString = null //"event"

const logResultCheck = false
const logTraversingCheck = false
const logFilter = (role: string, action: string, resource: string) =>
  logFilterString
    ? role === logFilterString ||
      action === logFilterString ||
      resource === logFilterString
    : true

const logResult = (
  user: User,
  resource: string,
  action: string,
  result: boolean | null,
  data?: Community | Userdata | GlobalData | Event,
  details?: string,
) => {
  logResultCheck &&
    logFilter("", action, resource) &&
    console.info(
      result ? "✅" : "❌",
      "## RESULT ##",
      result,
      resource,
      action,
      user,
      data,
      details,
    )
}

const logTraversing = (
  user: User,
  resource: string,
  action: string,
  granter: Role,
  result: boolean | null,
  data?: Community | Userdata | GlobalData | Event,
) => {
  logTraversingCheck &&
    logFilter(granter, action, resource) &&
    console.info(
      result ? "✅" : "❌",
      resource,
      action,
      result,
      user,
      data,
      granter,
    )
}

export function getRoleProps(resource: Role) {
  return roles[resource].properties ?? {}
}

export function hasPermission<Resource extends keyof Permissions>(
  user: User,
  resource: Resource,
  action: Permissions[Resource]["action"],
  data?: Permissions[Resource]["dataType"],
) {
  if (!user.roles || !user.id) {
    logResult(user, resource, action, false, data, "No information on user")
    return false
  }
  // Collect all permission values from applicable roles
  const permissionValues: (boolean | null)[] = []
  let hasApplicableRole = false

  user.roles.forEach((role) => {
    if (!isRole(role.role)) return

    if (
      role.subject === resource &&
      role.subjectId !== null &&
      role.subjectId !== data?.id
    ) {
      return
    }

    hasApplicableRole = true

    const permission = (roles as RolesWithPermissions)[role.role].permissions?.[
      resource
    ]?.[action]

    if (!permission && typeof permission !== "boolean") {
      // permissionValues.push(null)
      return
    }

    if (typeof permission === "boolean") {
      permissionValues.push(permission)

      logTraversing(user, resource, action, role.role, permission, data)
      return
    }

    const permissionGained =
      data != null &&
      permission({
        user,
        data,
        role,
        properties: (roles as RolesWithPermissions)[role.role].properties,
      })

    logTraversing(user, resource, action, role.role, permissionGained, data)

    permissionValues.push(permissionGained)
  })

  const hasFalse = permissionValues.includes(false)
  const hasTrue = permissionValues.includes(true)

  let result: boolean

  if (!hasApplicableRole) {
    result = false
  } else if (hasFalse) {
    result = false
  } else if (hasTrue) {
    result = true
  } else {
    // All permissions are null
    result = false
  }

  if (!hasApplicableRole) {
    logResult(user, resource, action, result, data, "No appropriate role")
  } else if (hasFalse) {
    logResult(user, resource, action, result, data, "Has false")
  } else if (hasTrue) {
    logResult(user, resource, action, result, data, "Has true")
  } else {
    logResult(user, resource, action, result, data, "All null")
  }

  return result
}
