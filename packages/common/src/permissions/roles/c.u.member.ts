import { RoleWithPermissionsAndProperties } from "../general"
import { eventJoin } from "./checks/event.join"
import { shareEventCommunity } from "./checks/event.shareEventCommunity"
import { check } from "./checks/check"

export const member: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {
      isMember: true,
      viewGeneral: true,
      view: true,
      communityUserData: true,
      shareUserGames: ({ user, data: community }) =>
        check(
          (community.allowShare ?? false) && user.id === community.viewedUserId,
        ),
      event: ({ data }) => check(data?.events === "all"),
    },
    userdata: {},
    global: {},
    event: {
      join: eventJoin,
      view: shareEventCommunity,
      viewPublic: shareEventCommunity,
    },
  },
  properties: {
    subject: "community",
    level: "user",
  },
}
