import { RoleWithPermissionsAndProperties } from "../general"
import { eventJoin } from "./checks/event.join"

export const host: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {},
    userdata: {},
    global: {},
    event: {
      join: eventJoin,
      isHost: true,
      isSuper: true,
      promoteCohost: true,
      update: true,
      delete: true,
      approve: true,
      shareUserGames: true,
    },
  },
  properties: {
    subject: "event",
    level: "super",
  },
}
