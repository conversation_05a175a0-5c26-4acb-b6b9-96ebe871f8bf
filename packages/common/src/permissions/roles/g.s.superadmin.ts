import { RoleWithPermissionsAndProperties } from "../general"

// removed permissions so that role would not fuck up everything

export const superadmin: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {},
    userdata: {},
    global: {},
    event: {},
  },
  properties: {
    subject: "global",
    level: "super",
  },
}

// TODO: return all permissions for Superadmon

/*
export const superadmin: RoleWithPermissionsAndProperties = {
  permissions: {
  community: {
    viewGeneral: true,
    view: true,
    update: true,
    delete: true,
    approve: true,
    communityUserData: true,
    promoteModer: true,
  },
  userdata: {
    view: true,
    update: true,
    delete: true,
  },
  global: {
    isUser: true,
    createCommunity: true,
    promoteAdmin: true,
  },
  event: {
      join: eventJoin,
      isHost: true,
      isParticipant: true,
      promoteCohost: true,
      view: true,
      viewPublic: true,
      update: true,
      delete: true,
      approve: true,
  },
  },
  properties: {
    subject: "global",
    level: "super",
  },
};
*/
