import { RoleWithPermissionsAndProperties } from "../general"

export const admin: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {
      viewGeneral: true,
      view: true,
      update: true,
      delete: true,
      approve: true,
      communityUserData: true,
      promoteModer: true,
    },
    userdata: {
      view: true,
      update: true,
      delete: true,
    },
    global: {
      isUser: true,
      createCommunity: true,
    },
    event: {
      promoteCohost: true,
      view: true,
      viewPublic: true,
      update: true,
      delete: true,
      approve: true,
    },
  },
  properties: {
    subject: "global",
    level: "super",
  },
}
