import { RoleSetting } from "./types";

export const getSetting = (settings: RoleSetting[], name: string) => {
  const setting = settings.find((setting) => setting.name === name);
  if (setting) {
    if (setting.value === "true") {
      return true;
    }

    if (
      setting.value === "false" ||
      setting.value === "" ||
      setting.value === "0" ||
      setting.value === null
    ) {
      return false;
    }

    if (parseFloat(setting.value) > 0) {
      return parseFloat(setting.value);
    }

    return setting.value;
  }

  return false;
};
