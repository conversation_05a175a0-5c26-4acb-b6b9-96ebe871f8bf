import { roles } from "../general"

export type RoleG = "superadmin" | "user" | "unverified" | "admin" | "trusted" // | "banned" ;
export type RoleC =
  | "owner"
  | "member"
  | "trustedmember"
  | "invited"
  | "moder"
  | "disgraced"

export type RoleE =
  | "host"
  | "cohost"
  | "interested" // thinking if want to participate
  | "participant" // will participate (approved/autoapproved)
  | "requested" // requested to participate
  | "reserved" // approved, but over the max capacity limit. Will be notified if someone cancels
  | "unwelcome" // rejected

export type Subject = "global" | "community" | "userdata" | "event"

export type Role = RoleG | RoleC | RoleE

export type RoleSetting = {
  name: string
  value: string
}

export interface RoleData {
  role: string | Role
  subject: Subject
  subjectId: number | null
  roleSettings?: RoleSetting[]
}

export type User = {
  roles?: RoleData[]
  id?: number
}

export function isRole(role: Role | string): role is Role {
  return roles[role as Role] !== undefined
}
export class Event {}
