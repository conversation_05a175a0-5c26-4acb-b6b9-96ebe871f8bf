import {
  type Role,
  type RoleC,
  type RoleData,
  type Subject,
  type RoleE,
} from "./types"
import { roles as allRoles } from "../general"

export const hasSubjectRole = (
  roles: RoleData[],
  role: Role,
  subject: Subject,
  subjectId?: number,
) =>
  roles.findIndex(
    (currentRole) =>
      role === currentRole.role &&
      currentRole.subject === subject &&
      currentRole.subjectId === subjectId,
  ) > -1

export const hasCommunityRole = (
  roles: RoleData[] | undefined | null,
  role: RoleC | RoleC[],
  subjectId: number,
) => {
  if (!roles) return false
  if (typeof role !== "string") {
    return role.some((r) => hasSubjectRole(roles, r, "community", subjectId))
  } else {
    return hasSubjectRole(roles, role, "community", subjectId)
  }
}

export const hasEventRole = (
  roles: RoleData[] | undefined | null,
  role: RoleE | RoleE[],
  subjectId: number,
) => {
  if (!roles) return false
  if (typeof role !== "string") {
    return role.some((r) => hasSubjectRole(roles, r, "event", subjectId))
  } else {
    return hasSubjectRole(roles, role, "event", subjectId)
  }
}

export const getSubjectRole = (
  subjectId: number,
  subject: Subject,
  roles: RoleData[],
) => {
  const role = roles.reduce(
    (determined, r) => {
      if (r.subject !== subject || r.subjectId !== subjectId) return determined
      if (!determined) return r

      const settingsCurrent = allRoles[r.role as Role]?.properties
      if (!settingsCurrent) return determined

      const settingsDetermined = determined
        ? allRoles[determined.role as Role]?.properties
        : {}

      if (settingsDetermined && settingsCurrent) {
        if ((settingsDetermined.level ?? -1) >= (settingsCurrent.level ?? 0)) {
          return r
        }

        if ((settingsDetermined.level ?? -1) < (settingsCurrent.level ?? 0)) {
          return determined
        }
      }

      return determined
    },
    undefined as RoleData | undefined,
  )
  if (role) return role.role
  return null
}

export const getEventRole = (subjectId: number, roles: RoleData[]) =>
  getSubjectRole(subjectId, "event", roles)

export const getCommunityRole = (subjectId: number, roles: RoleData[]) =>
  getSubjectRole(subjectId, "community", roles)
