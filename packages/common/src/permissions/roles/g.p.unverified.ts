import { RoleWithPermissionsAndProperties } from "../general"
import { check } from "./checks/check"

export const unverified: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {
      isWaiting: true,
      viewGeneral: ({ data }) =>
        check(data.openness === "publicLimited" || data.openness === "public"),
    },
    userdata: {
      view: ({ user, data }) => check(user.id === data.id),
      update: ({ user, data }) => check(user.id === data.id),
      delete: ({ user, data }) => check(user.id === data.id),
    },
    global: {},
    event: {},
  },
  properties: {
    subject: "event",
    level: "pending",
  },
}
