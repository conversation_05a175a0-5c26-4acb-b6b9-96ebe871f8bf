import { RoleWithPermissionsAndProperties } from "../general"
import { shareEventCommunity } from "./checks/event.shareEventCommunity"
import { check } from "./checks/check"

export const owner: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {
      isMember: true,
      update: true,
      delete: true,
      approve: true,
      promoteModer: true,
      invite: true,
      shareUserGames: true,
      event: ({ data }) => check(data?.events !== "none"),
    },
    userdata: {},
    global: {},
    event: {
      promoteCohost: shareEventCommunity,
      update: shareEventCommunity,
      delete: shareEventCommunity,
      approve: shareEventCommunity,
    },
  },
  properties: {
    subject: "community",
    level: "super",
  },
}
