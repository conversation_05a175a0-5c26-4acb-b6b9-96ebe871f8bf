import { RoleWithPermissionsAndProperties } from "../general"
import { eventJoin } from "./checks/event.join"
import { shareEventCommunity } from "./checks/event.shareEventCommunity"

export const moder: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {
      isMember: true,
      view: true,
      update: true,
      approve: true,
      invite: true,
      shareUserGames: true,
      event: ({ data }) => data?.events !== "none",
    },
    userdata: {},
    global: {},
    event: {
      join: eventJoin,
      promoteCohost: shareEventCommunity,
      update: shareEventCommunity,
      delete: shareEventCommunity,
      approve: shareEventCommunity,
    },
  },
  properties: {
    subject: "community",
    level: "super",
  },
}
