import { RoleWithPermissionsAndProperties } from "../general"
import { eventJoin } from "./checks/event.join"
import { check } from "./checks/check"

export const trustedmember: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {
      isMember: true,
      event: ({ data }) =>
        check(data?.events === "all" || data?.events === "trusted"),
    },
    userdata: {},
    global: {},
    event: {
      join: eventJoin,
    },
  },
  properties: {
    subject: "community",
    level: "super",
  },
}
