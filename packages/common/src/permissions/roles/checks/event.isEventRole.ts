import { RolePermissionCheckFunctionParams } from "../../general"
import { check } from "./check"

// verify if role being checked belongs to community of the event
export const isEventRole = ({
  data: event,
  role,
}: Pick<RolePermissionCheckFunctionParams<"event">, "data" | "role">):
  | boolean
  | null => {
  if (!role || role.subject !== "event") {
    return null
  }

  return check(event.id === role.subjectId)
}
