import { RolePermissionCheckFunctionParams } from "../../general"
import { check } from "./check"

// verify if role being checked belongs to community of the event
export const shareEventCommunity = ({
  data: event,
  role,
}: Pick<RolePermissionCheckFunctionParams<"event">, "data" | "role">):
  | boolean
  | null => {
  if (!event.hosts || !role || role.subject !== "community") {
    return null
  }

  return check(
    (event.openness === "community" || event.openness === "seniority") &&
      event.hosts.some((community) => community.id === role.subjectId),
  )
}
