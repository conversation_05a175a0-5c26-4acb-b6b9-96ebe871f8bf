import { shareEventCommunity } from "./event.shareEventCommunity"
import { RolePermissionCheckFunctionParams } from "../../general"

export const eventJoin = ({
  data: event,
  role,
  properties,
}: Pick<
  RolePermissionCheckFunctionParams<"event">,
  "data" | "role" | "properties"
>): boolean | null => {
  if (!event || !event.openness) {
    return false
  }

  if (!role) {
    return null
  }

  if (
    event.id === role.subjectId &&
    role.subject === "event" &&
    properties?.level === "user"
  ) {
    return false
  }

  if (
    event.state === "hidden" ||
    event.state === "ended" ||
    event.state === "cancelled"
  ) {
    return false
  }

  if (event.openness === "closed" || event.openness === "private") {
    return null
  }

  if (event.maxCapacity && event.maxCapacity > 0) {
    if (
      (event?.going ?? 0) + (event?.reserve ?? 0) >=
      event.maxCapacity + (event?.reserveCapacity ?? 0)
    ) {
      return null
    }
  }

  if (event.openness === "public") {
    return true
  }

  if (
    event.openness === "community" ||
    event.openness === "seniority" ||
    event.openness === "publicLimited"
  ) {
    return shareEventCommunity({ data: event, role })
  }

  return null
}
