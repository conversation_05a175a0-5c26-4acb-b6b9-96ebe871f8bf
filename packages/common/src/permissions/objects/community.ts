export type Community = {
  id: number;
  openness?: "public" | "publicLimited" | "private" | "closed";
  events?: "moders" | "all" | "trusted" | "none";
  viewedUserId?: number;
  allowShare?: boolean;
};

export interface CommunityPermissions {
  dataType: Community;
  action:
    | "isMember"
    | "isWaiting"
    | "isDenied"
    | "promoteModer"
    | "view"
    | "update"
    | "delete"
    | "invite"
    | "approve"
    | "viewGeneral"
    | "communityUserData"
    | "event"
    | "shareUserGames";
}
