export type Event = {
  // subjectId: number
  id?: number
  hosts?: { id: number }[]
  state?: string
  openness?: string
  maxCapacity?: number | null
  reserveCapacity?: number | null
  going?: number | null
  reserve?: number | null
  viewedUserId?: number
  allowShare?: boolean
}

export interface EventPermissions {
  dataType: Event
  action:
    | "isWatching"
    | "isParticipant"
    | "isWaiting"
    | "isRequested"
    | "isHost"
    | "isDenied"
    | "isReserved"
    | "isSuper"
    | "isCohost"
    | "promoteCohost"
    | "view"
    | "viewPublic"
    | "update"
    | "delete"
    | "join"
    | "approve"
    | "shareUserGames"
}
