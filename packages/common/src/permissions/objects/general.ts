import { UserdataPermissions } from "./userdata"
import { CommunityPermissions } from "./community"
import { GlobalPermissions } from "./global"
import { EventPermissions } from "./event"
import { RoleProperties } from "./roleProperties"

export type Permissions = {
  event: EventPermissions
  userdata: UserdataPermissions
  community: CommunityPermissions
  global: GlobalPermissions
}

export type PermissionsWithRoleProperties = Permissions & {
  roleProperties: RoleProperties
}
