{"name": "express", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "ts-node ./src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@tsconfig/node-lts": "^22.0.1", "@types/express": "^5.0.0", "express": "^4.21.2", "nodemon": "^3.1.7", "ts-node": "^10.9.2", "typescript": "^5.7.2", "@eslint/js": "^9.14.0", "eslint": "^9.14.0", "eslint-plugin-import": "^2.31.0", "globals": "^15.12.0", "prettier": "^3.3.3", "typescript-eslint": "^8.13.0"}}