<article>
  <h4>Couple</h4>
  <p><a href="?do=utils&action=couple">Couple</a></p>
  <p>Add one game as base and one existing base as expansion.</p>
  <h4>Split</h4>
  <p><a href="?do=utils&action=split">Split</a></p>
  <p>Remove connection between two base games that should not be there.</p>
  <h4>Expansion</h4>
  <p><a href="?do=utils&action=expansion">Expansion</a></p>
  <p>Set game as base-expansion and remove base-game entries</p>
</article>
<?php

function import($database, $SesClient)
{
    if (!isset($_GET['action'])) {
        return false;
    }

    switch ($_GET['action']) {
        case "couple":
            $file = "i_couple.php";
            break;
        case "split":
            $file = "i_split.php";
            break;
        case "expansion":
            $file = "i_expansion.php";
            break;
        default:
            return false;
    }

    require $file;
    subImport($database, $SesClient);
}