<?php

function subImport($database)
{
    if (!empty($_POST['base']) && (!empty($_POST['expansion']) || !empty($_POST['expansions']))) {
        $gameId = intval($_POST['base']);
        if (!empty($_POST['expansions'])) {
            $expansions = explode(',', $_POST['expansions']);
            foreach ($expansions as $expansion) {
                $expansionId = intval($expansion);
                couple($database, $gameId, $expansionId);
            }
        } else {
            $expansionId = intval($_POST['expansion']);
            couple($database, $gameId, $expansionId);
        }
        echo "<div>Job done!</div>";
    }

    renderForm();
}

function couple($database, $gameId, $expansionId)
{
    $database->update('games', ['type' => 'base-expansion'], ['id' => $expansionId]);
    $database->delete('agr_game2community2', ['game_id' => $expansionId]);

    $database->insert('game2expansion', ['game_id'      => $gameId,
                                         'expansion_id' => $expansionId]);

    $moveBaseUsers = $database->select('user2base_game', ['user_id'], ['game_id' => $gameId]);
    foreach ($moveBaseUsers as $moveBaseUser) {
        $userId = intval($moveBaseUser['user_id']);

        $alreadyExists = $database->select('user2base_game', ['game_id'], ['user_id' => $userId,
                                                                           'game_id' => $gameId]);
        if (!$alreadyExists)
            $database->insert('user2base_game', ['game_id' => $gameId,
                                                 'user_id' => $userId]);
    }

    $database->delete('user2base_game', ['game_id' => $expansionId]);

    // connected all expansions under this base-expansion to true base if they are not already connected

    $convertExpansions = $database->select('game2expansion', ['expansion_id'], ['game_id' => $expansionId]);

    foreach ($convertExpansions as $convertExpansion) {
        $subExpansionId = intval($convertExpansion['expansion_id']);

        $alreadyExists = $database->select('game2expansion', ['game_id'], ['expansion_id' => $subExpansionId,
                                                                           'game_id'      => $gameId]);

        if (!$alreadyExists)
            $database->insert('game2expansion', ['game_id'      => $gameId,
                                                 'expansion_id' => $subExpansionId]);
    }

    $database->delete('game2expansion', ['game_id' => $expansionId]);
}

function renderForm()
{
    ?>
  <article>
    <h3>Couple</h3>
    <form method="post" action="?do=utils&action=couple&run=1&log=1">
      <label>Base</label>
      <input type="number" name="base" value="" />
      <label>Expansion</label>
      <input type="number" name="expansion" value="" />
      <label>Expansions (separate with ",")</label>
      <input type="text" name="expansions" value="" />
      <input type="submit" value="Submit" />
    </form>
  </article>
    <?php
}