<?php
function subImport($database)
{
    if (isset($_POST['bases'])) {
        $bases = explode(",", $_POST['bases']);

        foreach ($bases as $base1) {
            $baseId = intval($base1);
            foreach ($bases as $base2) {
                $baseId2 = intval($base2);
                if ($baseId != $baseId2) {
                    $database->delete('game2expansion', ['game_id' => $baseId, 'expansion_id' => $base2]);
                }
            }
        }

        echo "<div>Job done!</div>";
    }

    renderForm();
}

function renderForm()
{
    ?>
  <article>
    <h3>Split</h3>
    <form method="post" action="?do=utils&action=split&run=1&log=1">
      <label>Bases (separate with ",")</label>
      <input type="text" name="bases" value="" />
    </form>
  </article>
    <?php
}