<?php

function subImport($database)
{
    if (isset($_POST['base'])) {
        $gameId = intval($_POST['base']);

        $database->update('games', ['type' => 'base-expansion'], ['id' => $gameId]);
        $database->delete('agr_game2community2', ['game_id' => $gameId]);

        $bases = $database->select('game2expansion', ['game_id'], ['expansion_id' => $gameId]);

        $moveBaseUsers = $database->select('user2base_game', ['user_id'], ['game_id' => $gameId]);
        foreach ($bases as $base) {
            foreach ($moveBaseUsers as $moveBaseUser) {
                $userId = intval($moveBaseUser['user_id']);

                $alreadyExists = $database->select('user2base_game', ['game_id'], ['user_id' => $userId,
                                                                                   'game_id' => $base['game_id']]);

                if (!$alreadyExists)
                    $database->insert('user2base_game', ['game_id' => $base['game_id'],
                                                         'user_id' => $userId]);
            }
        }

        $database->delete('user2base_game', ['game_id' => $gameId]);

        // connected all expansions under this base-expansion to true base if they are not already connected

        $convertExpansions = $database->select('game2expansion', ['expansion_id'], ['game_id' => $gameId]);

        foreach ($bases as $base) {
            foreach ($convertExpansions as $convertExpansion) {
                $subExpansionId = intval($convertExpansion['expansion_id']);

                $alreadyExists = $database->select('game2expansion', ['game_id'], ['expansion_id' => $subExpansionId,
                                                                                   'game_id'      => $base['game_id']]);

                if (!$alreadyExists)
                    $database->insert('game2expansion', ['game_id'      => $base['game_id'],
                                                         'expansion_id' => $subExpansionId]);
            }
        }

        $database->delete('game2expansion', ['game_id' => $gameId]);

        echo "<div>Job done!</div>";
    }

    renderForm();
}

function renderForm()
{
    ?>
  <article>
    <h3>Couple</h3>
    <form method="post" action="?do=utils&action=expansion&run=1&log=1">
      <label>Base => Expansion</label>
      <input type="number" name="base" value="" />
      <input type="submit" value="Submit" />
    </form>
  </article>
    <?php
}