<?php

function subImport($database) {
    $settings = getSettings($database);

    $maxAllowedRuntime = findSetting($settings['settings'], "cron:max_allowed_runtime");

    foreach ($settings['crons'] as $cron) {
        $timestamp = strtotime($cron['last_run']);
        if ($timestamp <= time() - $maxAllowedRuntime && $cron['is_on'] === 1) {
            $database->update("crons", ['is_running' => 0], ['id' => $cron['id']]);
        }
    }
}