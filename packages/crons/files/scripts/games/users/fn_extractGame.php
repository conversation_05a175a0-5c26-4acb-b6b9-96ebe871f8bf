<?php

function extractGame($database, $game, $user, $listType)
{

    $gameData = $database->get('games', ['type',
                                         'id',
                                         'bgg_id',], ['bgg_id' => intval($game['objectid']->__toString())]);
    $newEntry = false;

    if (empty($gameData)) {
        $newEntry = true;
        $newGameData = ["bgg_id" => $game['objectid']->__toString(),
                        "title"  => $game->name->__toString(),
                        "type"   => "new"];

        $database->insert("games", $newGameData);

        $gameData['id'] = $database->id();
    }

    $gameConnection = null;

    if (!$newEntry) {
        $gameConnection = $database->get('user2game', ['deleted',
                                                       'game_id'], ['bgg_collid' => intval($game['collid']->__toString())]);
    }

    $gameConnectionData = ["game_id"        => $gameData['id'],
                           "user_id"        => $user['id'],
                           "cal_play_count" => $game->numplays->__toString(),
                           "status"         => $listType,
                           "bgg_collid"     => $game['collid']->__toString(),
                           "rating"         => floatval($game->stats->rating['value']->__toString()),
                           "deleted"        => 0,
                           "bgg_data"       => json_encode(["wishlist_priority" => isset($item->status['wishlistpriority']) ? $game->status['wishlistpriority']->__toString() : null,
                                                            "preordered"        => isset($item->status['preordered']) ? $game->status['preordered']->__toString() : null,
                                                            "wishlist"          => isset($item->status['wishlistpriority']) ? $game->status['wishlist'] : null,
                                                            "own"               => $game->status['own'],])];

    if ($newEntry || $gameConnection == null) {
        $checkColId = $database->select('user2game', ['id'], ['bgg_collid' => $gameConnectionData['bgg_collid']]);
        if (empty($checkColId)) {
            $database->insert('user2game', $gameConnectionData);
        } else {
            $database->update('user2game', $gameConnectionData, ['bgg_collid' => $gameConnectionData['bgg_collid']]);
        }
    } else {
        $database->update('user2game', $gameConnectionData, ['bgg_collid' => $gameConnectionData['bgg_collid']]);

        if ($gameData['type'] === 'base' && isset($gameConnectionData['game_id'])) {
            $database->query("INSERT INTO `user2base_game` SET `user_id` = " . $gameConnectionData['user_id'] . " , `game_id` = " . $gameConnectionData['game_id'] . " ON DUPLICATE KEY UPDATE `game_id` = " . intval($gameConnectionData['game_id']));
        } else {
            $bases = $database->select('game2expansion', ['game_id'], ['expansion_id' => $gameConnectionData['game_id']]);

            foreach ($bases as $base) {
                $database->query("INSERT INTO `user2base_game` SET `user_id` = " . $gameConnectionData['user_id'] . " , `game_id` = " . $base['game_id'] . " ON DUPLICATE KEY UPDATE `game_id` = " . intval($base['game_id']));
            }
        }
    }

    // TODO: Add requests for game plays list
}