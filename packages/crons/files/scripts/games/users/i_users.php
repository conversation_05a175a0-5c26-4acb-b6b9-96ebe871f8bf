<?php

require "fn_extractGame.php";
require "fn_cleanUpDeleted.php";
require "fn_extractGames.php";
require "fn_updateGameList.php";
require "fn_getUsers.php";
require "fn_getPlays.php";
require "fn_updatePlays.php";
require "fn_updateOwnedCount.php";

function subImport($database, $SesClient)
{
    $settings = getSettings($database, "users");

    $userList = getUsers($database, $settings['crons']['items_per_batch'], $settings['crons']['refetch']);

    foreach ($userList as $user) {
        updateGameList($database, $user, $settings);
        getPlays($database, $user, $settings);
        updateOwnedCount($database, $user['id']);
        sleep(findSetting($settings['settings'], "cron:sleep_before_items"));
    }
    echo "Job done!";
}



