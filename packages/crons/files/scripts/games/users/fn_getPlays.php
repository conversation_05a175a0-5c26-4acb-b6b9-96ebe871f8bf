<?php

function getPlays($database, $user, $settings)
{
    libxml_use_internal_errors(true);

    $sleep_between_calls = 2;

    $page = 0;

    try {
        $page++;

        if ($user['last_updated'] !== null && $user['last_play_count'] > 0) {
            $lastUpdated = explode(" ", $user['last_updated'])[0];
        } else {
            $lastUpdated = "1970-01-01";
        }

        $xml = file_get_contents('https://www.boardgamegeek.com/xmlapi2/plays?mindate=' . $lastUpdated . '&username=' . $user['bgg_username'] . '&page=' . $page);
        $docs = new SimpleXMLElement($xml);
        $json = json_encode($docs);
        $array = json_decode($json, true);

        while (isset($array['play'])) {
            updatePlays($database, $array['play'], $user['id']);
            logStatus("Plays page: <strong>{$page}</strong> done!", 'process');

            sleep($sleep_between_calls);
            $page++;
            $xml = file_get_contents('https://www.boardgamegeek.com/xmlapi2/plays?mindate=' . $lastUpdated . '&username=' . $user['bgg_username'] . '&page=' . $page);
            $docs = new SimpleXMLElement($xml);
            $json = json_encode($docs);
            $array = json_decode($json, TRUE);
        }

        $database->update('users', ['last_play_count' => 1], ['id' => $user['id']]);

    } catch (Exception $e) {
        logStatus("No Data", 'error');
        var_dump($e);
    }
}