<?php

function getUsers($database, $max, $refetch): array
{
    return $database->query("
        SELECT 
            `id`, 
            `bgg_username`,
            `last_updated`,
            `last_play_count`
        FROM 
            `users`
        WHERE 
            (`last_updated` < '" . date("Y-m-d H:i:s", time() - ($refetch * 60)) . "' OR `last_updated` IS NULL)
        AND
            `bgg_username` IS NOT NULL
        AND 
            `active` = 1
        ORDER BY `last_updated` ASC
        LIMIT " . $max . "
            ")->fetchALl();
}