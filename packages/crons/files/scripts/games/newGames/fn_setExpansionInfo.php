<?php

function setExpansionInfo($database, $bggGame, $game)
{
    libxml_use_internal_errors(true);

    $selectType = null;

    $acceptableLinks = ['boardgameexpansion',
                        'boardgameintegration',
                        'boardgameaccessory',
                        'boardgamecompilation',// "boardgameimplementation"
    ];

    $me = $bggGame['id']->__toString();

    $oldest = $bggGame['id']->__toString();
    $family = [];

    $myType = $bggGame['type']->__toString();

    if (isset($bggGame->link)) {
        foreach ($bggGame->link as $link) {
            $linkType = $link['type']->__toString();
            if (in_array($linkType, $acceptableLinks)) {
                $check = true;
                if ($linkType === 'boardgameaccessory' && ($myType !== 'accessory' && $myType !== 'boardgameaccessory')) {
                    $check = false;
                }

                if ($linkType === 'boardgameexpansion' && ($myType !== 'expansion' && $myType !== 'boardgameexpansion')) {
                    $check = false;
                }

                if ($linkType === 'boardgamecompilation' && !isset($link['inbound'])) {
                    $check = false;
                }

                $linkId = $link['id']->__toString();

                $current = $database->select("games", ["bgg_id"], ["bgg_id" => $linkId, "type" => "base"]);

                if ($current) {
                    $family[] = $current[0]['bgg_id'];
                }

                if ($check && $oldest > $linkId) {
                    $oldest = $linkId;
                    $oldestInfo = $link;
                }
            }
        }

        if ($oldest != $game['bgg_id']) {
            switch ($myType) {
                case 'boardgame':
                    $selectType = 'base-expansion';
                    break;
                case 'boardgameexpansion':
                case 'expansion':
                    $selectType = 'expansion';
                    break;
                case 'boardgameaccessory':
                case 'accessory':
                    $selectType = 'accessory';
                    break;
            }

            if (!in_array($oldest, $family)) {
                $family[] = $oldest;
            }

            $database->query("DELETE FROM `game2expansion` WHERE game_id = " . $game['id'] . " OR expansion_id = " . $game['id']);
            $database->update('games', ['type' => $selectType], ['id' => $game['id']]);

            foreach ($family as $f) {
                $id = getMainGameInfo($database, $f, $oldestInfo);
                $database->insert('game2expansion', ['expansion_id' => $game['id'], 'game_id' => $id]);
            }
        }
    }

    if (!$selectType) {
        $database->update('games', ['type' => 'base'], ['id' => $game['id']]);
        $game['type'] = 'base';
        selfLink($database, $game);
        createBaseGames($database, $game);
        checkForExpansions($database, $game);
    }
}