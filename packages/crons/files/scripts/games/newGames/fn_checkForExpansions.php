<?php

function checkForExpansions($database, $game)
{
    $expansions = $database->select("game2expansion", ['expansion_id'], ['game_id' => $game['id']]);

    foreach ($expansions as $expansion) {
        if ($game['type'] === "base") {
            updateIfBase($database, $game, $expansion);
        } else {
            // updateIfExpansion($database, $game, $expansion);
        }
    }
}

function updateIfBase($database, $game, $expansion)
{
    $usersWithGame = $database->select("user2game", ['user_id'], ['game_id' => $expansion['expansion_id']]);

    foreach ($usersWithGame as $user) {
        $database->query("INSERT INTO `user2base_game` SET `user_id` = " . $user['user_id'] . " , `game_id` = " . $game['id'] . " ON DUPLICATE KEY UPDATE `game_id` = " . $game['id']);
    }
}

function updateIfExpansion($database, $game, $expansion) {}