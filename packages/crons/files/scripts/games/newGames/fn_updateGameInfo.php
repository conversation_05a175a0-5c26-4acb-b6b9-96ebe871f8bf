<?php

function updateGameInfo($database, $game, $SesClient, $settings)
{
    try {
        $database->action(function ($database) use (&$game, &$SesClient, &$settings) {
            $bggGame = getGameXml($game['bgg_id'], findSetting($settings['settings'], "cron:user_init_call_sleep"));

            updateBaseData($database, $game, $bggGame);
            downloadImage($SesClient, $bggGame->image->__toString(), $game['id'], 'game', findSetting($settings['settings'], "img:game_image_path"), $game['title'], 250);
            setExpansionInfo($database, $bggGame, $game);
            setGameUpdated($database, $game);
            setTagInfo($database, $bggGame, $game);
            improveTags($database, $game['id']);

            if (!isset($_GET['run'])) {
                throw new Exception('Just stop!!!');
            }
        });
    } catch (Exception $e) {
        echo '<pre>' . var_dump($database->log()) . '</pre>';
        echo "Failed with: ", $e->getMessage(), "\n";
    }
}