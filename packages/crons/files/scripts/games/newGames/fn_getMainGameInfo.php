<?php

function getMainGameInfo($database, $gameId, $oldestInfo)
{
    $game = $database->get('games', ['id'], ['bgg_id' => $gameId]);
    if ($game) {
        return $game['id'];
    } else {
        $database->insert('games', ['bgg_id' => $gameId,
                                    'type'   => 'new',
                                    'title'  => $oldestInfo['value']->__toString()]);
        return $database->id();
    }
}