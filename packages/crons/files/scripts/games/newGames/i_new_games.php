<?php
require __DIR__ . "/../common.php";
require "constants.php";
require "fn_createBaseGames.php";
require "fn_getMainGameInfo.php";
require "fn_getNewGames.php";
require "fn_selfLink.php";
require "fn_setExpansionInfo.php";
require "fn_setTagInfo.php";
require "fn_updateGameInfo.php";
require "fn_checkForExpansions.php";

function subImport($database, $SesClient)
{
    $settings = getSettings($database, "new_games");

    $gameList = getNewGames($database, $settings);

    foreach ($gameList as $game) {
        updateGameInfo($database, $game, $SesClient, $settings);
        sleep(SLEEP_BETWEEN_GAMES);
    }

    echo "Job done!";
}

