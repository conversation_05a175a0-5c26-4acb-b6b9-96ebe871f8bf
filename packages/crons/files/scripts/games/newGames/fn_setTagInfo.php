<?php

function setTagInfo($database, $bggGame, $game)
{
    libxml_use_internal_errors(true);

    $acceptableLinks = ['boardgamefamily',
                        'boardgamemechanic',
                        'boardgamecategory',];

    $titleMap = ['boardgamefamily'   => 'family',
                 'boardgamemechanic' => 'mechanic',
                 'boardgamecategory' => 'category',];

    if (isset($bggGame->link)) {
        foreach ($bggGame->link as $link) {
            if (in_array($link['type'], $acceptableLinks)) {
                $tag = $database->get('tags', ['id'], ['bgg_id' => $link['id']->__toString()]);
                if (!$tag) {
                    $tag = ['bgg_id' => $link['id']->__toString(),
                            'title'  => $link['value']->__toString(),
                            'type'   => $titleMap[$link['type']->__toString()]];

                    $database->insert('tags', $tag);

                    $tag['id'] = $database->id();
                }

                $database->query('INSERT INTO `game2tag` SET `game_id` = ' . $game['id'] . ', `tag_id` = ' . $tag['id'] . ' ON DUPLICATE KEY UPDATE `game_id` = ' . $game['id']);
            }
        }
    }
}