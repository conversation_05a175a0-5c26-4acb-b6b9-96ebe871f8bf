<?php

function updateCommunity($database, $community, $settings)
{
    // get all community users
    $relatedUsers = $database->query("
    SELECT
        `permission_user2role`.`user_id`
    FROM 
        `permission_user2role`
    INNER JOIN 
        `user2community` ON `user2community`.`user_id` = `permission_user2role`.`user_id`
        AND `user2community`.`community_id` = " . $community['id'] . "
    WHERE
        `subject_id` = " . $community['id'] . "
    AND
        `permission_user2role`.`role_id` IN ('member')
    AND
        `permission_user2role`.`subject` = 'community' 
    AND 
        `user2community`.`shareMyGames` = 1
    ")->fetchALl();

    $userGames = [];

    // get all games for each user
    foreach ($relatedUsers as $relatedUser) {
        $userGames[$relatedUser['user_id']] = getUserGames($database, $relatedUser);
    }

    // create game list with all users for game from this community
    $gameList = [];
    foreach ($userGames as $user => $userGameData) {
        foreach ($userGameData as $gameData) {
            if (!isset($gameList[$gameData['game_id']])) {
                $gameList[$gameData['game_id']] = [];
            }

            $gameList[$gameData['game_id']][$user] = $gameData;
        }
    }

    // save all data for all things
    foreach ($gameList as $gameId => $gameData) {
        $tags = getTagList($database, $gameId, $settings);
        saveGameData($database, $gameData, $gameId, $community['id'], $tags, $settings);
    }

    // remove all entries with no users
    stripBlanks($database, $gameList, $community['id']);
}