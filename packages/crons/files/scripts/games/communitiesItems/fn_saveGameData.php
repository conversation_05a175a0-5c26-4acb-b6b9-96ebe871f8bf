<?php

function saveGameData($database, $gameData, $gameId, $communityId, $tags, $settings)
{
    // select existing community data for game
    $link = $database->select("agr_item2community", ["game_id", "agr_users"], ["game_id"      => $gameId,
                                                                               "community_id" => $communityId]);

    $gameUserData = [];
    foreach ($gameData as $userId => $user2gameData) {
        $gameUserData[] = $userId;
    }

    $tagData = [];
    $tagExpansionData = [];
    foreach ($tags as $tag) {
        if ($tag['type_id'] == findSetting($settings['settings'], "tags:expansion_tag_type_id")) {
            $tagExpansionData = $tag['tag_id'];
        } else {
            $tagData[] = $tag['tag_id'];
        }
    }

    $gameResultData = ['agr_users'          => json_encode($gameUserData),
                       'agr_tags'           => json_encode($tagData),
                       'agr_expansion_tags' => json_encode($tagExpansionData),
                       'user_count'         => count($gameUserData)];

    if (!empty($link)) {
        if ($link[0]['agr_users'] != $gameResultData['agr_users']) {
            $gameResultData['news'] = date("Y-m-d H:i:s", time());
        }

        $database->update("agr_item2community", $gameResultData, ["game_id"      => $gameId,
                                                                  "community_id" => $communityId]);
    } else {
        $database->insert("agr_item2community", [...$gameResultData,
                                                 "news"         => date("Y-m-d H:i:s", time()),
                                                 "game_id"      => $gameId,
                                                 "community_id" => $communityId]);
    }
}
