<?php

require "common.php";

function subImport($database, $SesClient)
{
    $games_all = $database->query("
    SELECT
    `id`
    FROM
        `games`
    ");

    foreach ($games_all as $game) {
        downloadImage($SesClient, $item->image, $game['id'], 'game', 'gallery/games/', $game['title'], 250);
        downloadImage($SesClient, $game['id'], "game");
    }
}




/*
function subImport($database, $SesClient)
{
    $games_all = $database->query("
    SELECT 
    `user_id`,
    `game_id`,
    `type`
    FROM
        `user2game`
    INNER JOIN 
        `games` ON `user2game`.`game_id` = `games`.`id`
     WHERE
         `games`.`type` = 'base'
    ");

    foreach ($games_all as $game) {
        $database->query("INSERT INTO `user2base_game` SET `user_id` = " . $game['user_id'] . " , `game_id` = " . $game['game_id'] . " ON DUPLICATE KEY UPDATE `game_id` = " . $game['game_id']);
    }
}
*/

/*
function subImport($database, $SesClient)
{
    $games_all = $database->select("games", ["id"]);

    foreach ($games_all as $game) {
        $database->insert("game2expansion", ["game_id" => $game["id"], "expansion_id" => $game['id']]);
    }
}
*/

/*

function subImport($database, $SesClient)
{
    $games_all = $database->select("games", ["id", "bgg_info"]);

    foreach ($games_all as $game) {
        autoTags($database, json_decode($game['bgg_info'], true), $game['id']);
    }
}
*/

/*
 *
 * Fix Broken tags, most probably never will be needed again
 *
function subImport($database, $SesClient)
{
    $brokenTags = $database->select("tags", ["id", "category", "bgg_id"], ["title" => ""]);

    foreach ($brokenTags as $tag) {
        $type = "boardgame" . $tag["category"];

        var_dump($tag);

        $fileHML = file_get_contents("https://boardgamegeek.com/" . $type . "/" . $tag["bgg_id"]);

        $start = strpos($fileHML, '<meta name="title" content="');
        $end = strpos($fileHML, '"', $start + 28);
        $sting = substr($fileHML, $start + 28, $end - $start - 28);

        $database->update("tags", ["title" => $sting, "display_title" => $sting], ["id" => $tag["id"]]);
    }
}
*/