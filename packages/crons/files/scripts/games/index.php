<?php

function import($database, $SesClient)
{
    if (!isset($_GET['action'])) {
        return false;
    }

    switch ($_GET['action']) {
        case "users":
            $file = __DIR__ . "/users/i_users.php";
            break;
        case "newgames":
            $file = __DIR__ . "/newGames/i_new_games.php";
            break;
        case "games":
            $file = "i_games.php";
            break;
        case "communities_games":
            $file = __DIR__ . "/communities/i_populate_communities_games.php";
            break;
        case "communities_items":
            $file = __DIR__ . "/communitiesItems/i_populate_communities_items.php";
            break;
        case "base_games":
            $file = __DIR__ . "/baseGames/i_base_games.php";
            break;
        case "run_cron_script":
            $file = "i_run.php";
            break;
        case "fix":
            $file = "i_fix.php";
            break;
        case "reset":
            $file = "i_reset.php";
            break;
        default:
            return false;
    }

    require $file;
    subImport($database, $SesClient);
}