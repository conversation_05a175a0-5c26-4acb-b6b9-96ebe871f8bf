<?php

function subImport($database, $SesClient)
{
    $settings = getSettings($database, "users");

    $userList = getUsers($database, $settings['crons']['items_per_batch'], $settings['crons']['refetch']);

    foreach ($userList as $user) {
        updateGameList($database, $user, $settings);
        sleep(findSetting($settings['settings'], "cron:sleep_before_items"));
    }
    echo "Job done!";
}

function getUsers($database, $max, $refetch): array
{
    return $database->query("
        SELECT 
            `id`, 
            `bgg_username`
        FROM 
            `users`
        WHERE 
            (`last_updated` < '" . date("Y-m-d H:i:s", time() - ($refetch * 60)) . "' OR `last_updated` IS NULL)
        AND
            `bgg_username` IS NOT NULL
        AND 
            `active` = 1
        ORDER BY `last_updated` ASC
        LIMIT " . $max . "
            ")->fetchALl();
}

function updateGameList($database, $user, $settings)
{
    $updateTime = date("Y-m-d H:i:s", time());

    $database->insert('user_update_chart', ['user_id' => $user['id'],
                                            'status'  => 1,
                                            'ran'     => $updateTime]);

    $latestUpdateId = $database->id();

    try {
        $database->action(function ($database) use (&$user, &$updateTime, &$latestUpdateId, &$settings) {

            $xml_link = 'https://www.boardgamegeek.com/xmlapi2/collection?username=' . $user['bgg_username'] . '&stats=1&own=1';
            $doc = RequestFile::run($xml_link, findSetting($settings['settings'], "cron:user_init_call_sleep"));

            if (!$doc) {
                throw new Exception('Failed to get XML');
            }

            cleanUpDeleted($database, $doc, $user);
            extractGames($database, $doc, $user);

            $database->update('users', ['last_updated' => $updateTime], ['id' => $user['id']]);

            if (!isset($_GET['run'])) {
                throw new Exception('Just stop!!!');
            }

            $database->update("user_update_chart", ['status' => 2], ['id' => $latestUpdateId]);
        });
    } catch (Exception $e) {
        $database->update("user_update_chart", ['status' => 3], ['id' => $latestUpdateId]);
        echo '<pre>' . var_dump($database->log()) . '</pre>';
        echo "Failed with: ", $e->getMessage(), "\n";
    }
}

function cleanUpDeleted($database, $doc, $user)
{
    $currentGames = $database->select('user2game', ['bgg_collid', 'game_id'], ['user_id' => $user['id'],
                                                                               'deleted' => 0]);

    if (empty($currentGames)) {
        return;
    }

    foreach ($currentGames as $game) {
        $gameFound = false;
        foreach ($doc->item as $item) {
            if ($item['collid'] == $game['bgg_collid']) {
                $gameFound = true;
                break;
            }
        }
        if (!$gameFound) {
            $database->update('user2game', ['deleted' => 1], ['bgg_collid' => $game['bgg_collid']]);
            $database->update('user2base_game', ['deleted' => 1], ['game_id' => $game['game_id']]);
        }
    }
}

function extractGames($database, $doc, $user, $listType = 1)
{
    for ($a = 0; $a < $doc['totalitems']; $a++) {
        $item = $doc->item[$a];
        extractGame($database, $item, $user, $listType);
    }
}

function extractGame($database, $game, $user, $listType)
{

    $gameData = $database->get('games', ['type',
                                         'id',
                                         'bgg_id',], ['bgg_id' => intval($game['objectid']->__toString())]);
    $newEntry = false;

    if (empty($gameData)) {
        $newEntry = true;
        $newGameData = ["bgg_id" => $game['objectid']->__toString(),
                        "title"  => $game->name->__toString(),
                        "type"   => "new"];

        $database->insert("games", $newGameData);

        $gameData['id'] = $database->id();
    }

    $gameConnection = null;

    if (!$newEntry) {
        $gameConnection = $database->get('user2game', ['deleted',
                                                       'game_id'], ['bgg_collid' => intval($game['collid']->__toString())]);
    }

    $gameConnectionData = ["game_id"        => $gameData['id'],
                           "user_id"        => $user['id'],
                           "cal_play_count" => $game->numplays->__toString(),
                           "status"         => $listType,
                           "bgg_collid"     => $game['collid']->__toString(),
                           "rating"         => floatval($game->stats->rating['value']->__toString()),
                           "deleted"        => 0,
                           "bgg_data"       => json_encode(["wishlist_priority" => isset($item->status['wishlistpriority']) ? $game->status['wishlistpriority']->__toString() : null,
                                                            "preordered"        => isset($item->status['preordered']) ? $game->status['preordered']->__toString() : null,
                                                            "wishlist"          => isset($item->status['wishlistpriority']) ? $game->status['wishlist'] : null,
                                                            "own"               => $game->status['own'],])];

    if ($newEntry || $gameConnection == null) {
        $database->insert('user2game', $gameConnectionData);
    } else {
        $database->update('user2game', $gameConnectionData, ['bgg_collid' => $gameConnectionData['bgg_collid']]);

        if ($gameData['type'] === 'base' && isset($gameConnectionData['game_id'])) {
            $database->query("INSERT INTO `user2base_game` SET `user_id` = " . $gameConnectionData['user_id'] . " , `game_id` = " . $gameConnectionData['game_id'] . " ON DUPLICATE KEY UPDATE `game_id` = " . intval($gameConnectionData['game_id']));
        } else {
            $bases = $database->select('game2expansion', ['game_id'], ['expansion_id' => $gameConnectionData['game_id']]);

            foreach ($bases as $base) {
                $database->query("INSERT INTO `user2base_game` SET `user_id` = " . $gameConnectionData['user_id'] . " , `game_id` = " . $base['game_id'] . " ON DUPLICATE KEY UPDATE `game_id` = " . intval($base['game_id']));
            }
        }
    }

    // TODO: Add requests for game plays list
}