<?php
require "common.php";
define("MAX_COMMUNITIES_PER_CALL", 1000);
define("LIST_UPDATE_INTERVAL", 1); // TODO: replace with appropriate interval (1 - 2 h ?)

/*
 *
 * read all expansion users and expansion tags and them to the base game
 *
 * add how many copies each user has in data!!!
 *
 */
function subImport($database, $SesClient)
{
    $communities = getCommunities($database);

    foreach ($communities as $community) {
        updateCommunity($database, $community);
    }

    echo "Job done!";
}

function getCommunities($database)
{
    return $database->query("
        SELECT 
            `id`
        FROM 
            `community`
        WHERE 
            (`last_updated` < '" . date("Y-m-d H:i:s", time() - LIST_UPDATE_INTERVAL) . "' OR `last_updated` IS NULL)
        LIMIT " . MAX_COMMUNITIES_PER_CALL)->fetchALl();

    //
}

function updateCommunity($database, $community)
{
    // get all community users
    $relatedUsers = $database->query("
    SELECT
        `user_id`,
        `users`.`name`,
        `users`.`color`,
        `users`.`avatar`
    FROM 
        `user2community`    
    INNER JOIN `users` ON `user2community`.`user_id` = `users`.`id`
    WHERE
        `community_id` = " . $community['id'] . "
    AND
        `user2community`.`status` IN ('user', 'moder', 'owner')
    AND
        `user2community`.`shareMyGames` = 1
    ")->fetchALl();;

    // get all games for each user
    foreach ($relatedUsers as $relatedUser) {
        $userGames[$relatedUser['user_id']] = ['games' => getUserGames($database, $relatedUser),
                                               'user'  => $relatedUser];
    }

    // create game list with all users for game from this community
    $gameList = [];
    foreach ($userGames as $user => $data) {
        foreach ($data['games'] as $game) {
            if (isset($gameList[$game['game_id']])) {
                $alreadyExists = false;

                foreach ($gameList[$game['game_id']] as $gameId => $seeIfExists) {
                    if ($seeIfExists['user']['user_id'] == $user) {
                        $alreadyExists = true;
                    }
                }

                if (!$alreadyExists) {
                    $gameList[$game['game_id']][] = ['user' => $data['user'],
                                                     'game' => $game];
                }
            } else {
                $gameList[$game['game_id']] = [['user' => $data['user'],
                                                'game' => $game]];
            }
        }
    }

    // save all data for users
    foreach ($gameList as $game => $data) {
        $tags = getTagList($database, $game);
        saveGameData($database, $data, $game, $community['id'], $tags);
    }

    // remove all entries with no users
    stripBlanks($database, $gameList, $community['id']);
}

function stripBlanks($database, $gameList, $communityId)
{
    $links = $database->select("game2community", ["game_id"], ["community_id" => $communityId]);

    foreach ($links as $link) {
        if (!isset($gameList[$link['game_id']])) {
            $database->delete("game2community", ["game_id"      => $link['game_id'],
                                                 'community_id' => $communityId]);
        }
    }
}

function saveGameData($database, $data, $gameId, $communityId, $tags)
{
    $expansionInfo = getExpansionInfo($database, $gameId);
    $gameData = [];

    // select existing community data for game
    $link = $database->select("game2community", ["game_id",
                                                 "data"], ["game_id"      => $gameId,
                                                           "community_id" => $communityId]);

    $shouldUpdateTimestamp = false;
    $linkData = null;

    if (!empty($link)) {
        $linkData = json_decode($link[0]['data'], true);
    }

    $gameType = "other";

    // prep insert data per user
    foreach ($data as $datum) {
        $itemData = ['id'          => $datum['user']['user_id'],
                     'name'        => $datum['user']['name'],
                     'color'       => $datum['user']['color'],
                     'avatar'      => $datum['user']['avatar'],
                     'rating'      => $datum['game']['rating'],
                     'last_play'   => $datum['game']['last_play'],
                     'play_count'  => $datum['game']['play_count'],
                     'portability' => $datum['game']['portability'],
                     'events'      => $datum['game']['events'],
                     'knows_rules' => $datum['game']['knows_rules'],];

        $gameType = $datum['game']['type'];

        $gameData[] = $itemData;
    }

    if ($linkData) {
        $shouldUpdateTimestamp = verifyUpdate($gameData, $linkData);
    }

    $userCount = count($data);

    $tagData = [];
    foreach ($tags as $tag) {
        $tagData[] = $tag['tag_id'];
    }

    $gameData = ['users' => $gameData,
                 'tags'  => $tagData,];

    if (!empty($link)) {
        $jsonData = json_encode($gameData);
        if ($shouldUpdateTimestamp || $link[0]['data'] != $jsonData) {
            $updateData = ["data"       => $jsonData,
                           'user_count' => $userCount];

            if ($shouldUpdateTimestamp) {
                $updateData['news'] = date("Y-m-d H:i:s", time());
            }

            $database->update("game2community", $updateData, ["game_id"      => $gameId,
                                                              "community_id" => $communityId]);
        }
    } else {
        $database->insert("game2community", ["data"         => json_encode($gameData),
                                             "news"         => date("Y-m-d H:i:s", time()),
                                             'user_count'   => $userCount,
                                             "game_id"      => $gameId,
                                             "game_type"    => $gameType,
                                             "community_id" => $communityId]);
    }
}

function verifyUpdate($newData, $linkData)
{
    $newUser = null;
    $otherChanges = false;

    foreach ($newData as $datum) {
        /*
         *  TODO any new user will trigger this. Must think of how to solve.
         */
        foreach ($linkData['users'] as $user) {
            if ($user['id'] == $datum['id']) {
                $newUser = $user;
            }
        }

        if ($newUser) {
            if ($newUser['play_count'] != $datum['play_count']) {
                $otherChanges = true;
            }

            if ($newUser['last_play'] != $datum['last_play']) {
                $otherChanges = true;
            }

            if ($newUser['rating'] != $datum['rating']) {
                $otherChanges = true;
            }

            if ($newUser['knows_rules'] != $datum['knows_rules']) {
                $otherChanges = true;
            }

            /* TODO should implement expansion count. Rest less important
            if ($newUser['expansionCount'] != $newData['expansionCount']) {
                $otherChanges = true;
            }
            */
        }
    }

    return $newUser === null || $otherChanges;
}

function getTagList($database, $gameId)
{
    return $database->query("
    SELECT
        `tag_id`
    FROM 
        `game2tag`
    INNER JOIN `tags` ON `game2tag`.`tag_id` = `tags`.`id`
    WHERE
        `game_id` = " . $gameId . "
    AND
        `visible` = '1'
    ")->fetchALl();
}

function getUserGames($database, $relatedUser)
{
    return array_unique($database->query("
    SELECT
        `game_id`,
        `rating`,
        `last_play`,
        `play_count`,
        `knows_rules`,
        `portability`,
        `events`,
        `type`
    FROM 
        `user2game`
    INNER JOIN `games` ON `user2game`.`game_id` = `games`.`id`
    WHERE
        `user_id` = " . $relatedUser['user_id'] . "
    AND 
        `deleted` = '0'
    ")->fetchALl(), SORT_REGULAR);
}

function getExpansionInfo($database, $gameId)
{
    $expansions = $database->query("SELECT 
    `expansion_id`,
FROM 
    `game2expansion`
WHERE
    `game2expansion`.`game_id` = " . $gameId . "`
    ");

    $globalUsers = [];
    $globalTags = [];

    foreach ($expansions as $expansion) {
        $users = $database->select("user2game", ['user_id'], ["game_id" => $expansion['expansion_id']]);
        $tags = $database->select("game2tag", ['tag_is'], ["game_id" => $expansion['expansion_id']]);

        array_push($globalUsers, $users);
        array_push($globalTags, $tags);
    }

    return [array_unique($globalUsers, SORT_REGULAR),
            array_unique($globalTags, SORT_REGULAR),];
}