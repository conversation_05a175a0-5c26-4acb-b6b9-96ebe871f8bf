<?php
require "common.php";

function subImport($database, $SesClient): void
{
    $settings = getSettings($database, "existing_games");

    $gameList = getGames($database, $settings['crons']['items_per_batch'], $settings['crons']['refetch']);

    $availableImages = getExistingImages($SesClient, findSetting($settings['settings'], "img:game_image_path"));

    foreach ($gameList as $game) {
        updateGameInfo($database, $game, $SesClient, $availableImages, $settings);
        improveTags($database, $game['id']);
        sleep(findSetting($settings['settings'], "cron:sleep_before_items"));
    }

    echo "Job done!";
}

function getGames($database, $max, $refetch): array
{
    return $database->query("
        SELECT 
            `id`, 
            `bgg_id`,
            `title`
        FROM 
            `games`
        WHERE 
            `type` != 'new'
        AND
            `last_updated` < '" . date("Y-m-d H:i:s", time() - ($refetch * 60)) . "'
        ORDER BY `last_updated` ASC
        LIMIT " . $max)->fetchALl();
}

function updateGameInfo($database, $game, $SesClient, $availableImages, $settings): void
{
    try {
        $database->action(function ($database) use (&$game, &$SesClient, &$availableImages, &$settings) {
            $bggGame = getGameXml($game['bgg_id'], findSetting($settings['settings'], "cron:user_init_call_sleep"));

            updateBaseData($database, $game, $bggGame);
            setGameUpdated($database, $game);
            // update all game bases that could be for this game
            setExpansionInfo($database, $bggGame, $game);

            if (!in_array(findSetting($settings['settings'], "img:game_image_path") . $game['id'] . '.jpg', $availableImages)) {
                downloadImage($SesClient, $bggGame->image->__toString(), $game['id'], 'game', findSetting($settings['settings'], "img:game_image_path"), $game['title'], 250);
            }

            updateTagInfo($database, $bggGame, $game);

            if (!isset($_GET['run'])) {
                throw new Exception('Just stop!!!');
            }
        });
    } catch (Exception $e) {
        echo '<pre>' . var_dump($database->log()) . '</pre>';
        echo "Failed with: ", $e->getMessage(), "\n";
    }
}

function setExpansionInfo($database, $bggGame, $game): void
{
    libxml_use_internal_errors(true);

    $acceptableLinks = ['boardgameexpansion',
                        'boardgameintegration',
                        'boardgameaccessory',
                        'boardgamecompilation'];

    $family = [];

    if (isset($bggGame->link)) {
        foreach ($bggGame->link as $link) {
            $linkType = $link['type']->__toString();
            if (in_array($linkType, $acceptableLinks)) {

                $linkId = $link['id']->__toString();
                $current = $database->select("games", ["id"], ["bgg_id" => $linkId, "type" => "base"]);

                if ($current) {
                    $family[] = $current[0]['id'];
                }
            }
        }

        foreach ($family as $f) {
            $database->query("INSERT game2expansion SET `expansion_id` = " . $game['id'] . ", `game_id` =" . $f . " ON DUPLICATE KEY UPDATE expansion_id = " . $game['id']);
        }
    }
}

function updateTagInfo($database, $bggGame, $game): void
{
    libxml_use_internal_errors(true);

    $acceptableLinks = ['boardgamefamily',
                        'boardgamemechanic',
                        'boardgamecategory',];

    $titleMap = ['boardgamefamily'   => 'family',
                 'boardgamemechanic' => 'mechanic',
                 'boardgamecategory' => 'category',];

    if (isset($bggGame->link)) {
        foreach ($bggGame->link as $link) {
            if (in_array($link['type'], $acceptableLinks)) {
                $tag = $database->get('tags', ['id', 'title'], ['bgg_id' => $link['id']->__toString()]);
                if (!$tag) {
                    $tag = ['bgg_id' => $link['id']->__toString(),
                            'title'  => $link['value']->__toString(),
                            'type'   => $titleMap[$link['type']->__toString()]];

                    $database->insert('tags', $tag);

                    $tag['id'] = $database->id();
                }

                $tagLink = $database->get('game2tag', ['game_id'], ['game_id' => $game['id'], 'tag_id' => $tag['id']]);

                if (!$tagLink) {
                    $database->insert('game2tag', ['game_id' => $game['id'], 'tag_id' => $tag['id']]);
                }
            }
        }
    }
}