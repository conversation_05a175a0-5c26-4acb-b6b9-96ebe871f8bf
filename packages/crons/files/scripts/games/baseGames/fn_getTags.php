<?php

function getTags($database, $game, $settings)
{
    $expansions = $database->query("
    SELECT
        `games`.`id`,
        `title`,
        `type`,
        `bgg_id`
    FROM 
        `game2expansion`
    INNER JOIN 
        `games` ON `game2expansion`.`expansion_id` = `games`.`id`
    INNER JOIN  `user2game`  ON (
        `game2expansion`.`expansion_id` = `user2game`.`game_id`
    AND 
        `user2game`.`user_id` = " . $game['user_id'] . "
        )
    WHERE
        `game2expansion`.`game_id` = " . $game['game_id'] . "
    AND
        `visible` = '1'
    ")->fetchALl();

    $expansionId = [];

    foreach ($expansions as $expansion) {
        $expansionId[] = $expansion['id'];
    }

    $tags = $database->query("
        SELECT
            `id`
        FROM 
            `game2tag`
        INNER JOIN `tags` ON `game2tag`.`tag_id` = `tags`.`id`
        WHERE
            `type_id` !=  " . findSetting($settings['settings'], "tags:expansion_tag_type_id") . "
        AND
            `tags`.`visible` = 1
        AND            
            `game2tag`.`game_id` IN (" . implode(",", $expansionId) . ")")->fetchAll();

    return $tags;
}