<?php

function getGames($database, $max, $refetch): array
{
    return $database->query("
        SELECT 
            `game_id`,
            `user_id`,
            `calc_tags`
        FROM 
            `user2base_game`
        WHERE
            (`last_updated` < '" . date("Y-m-d H:i:s", time() - ($refetch * 60)) . "' OR `last_updated` IS NULL)
        ORDER BY `last_updated` ASC
        LIMIT " . $max)->fetchALl();
}