<?php
require "fn_getGames.php";
require "fn_getTags.php";
require "fn_tagsMerged.php";
function subImport($database): void
{
    $settings = getSettings($database, "base_games");

    $gameList = getGames($database, $settings['crons']['items_per_batch'], $settings['crons']['refetch']);

    foreach ($gameList as $game) {
        $tags = getTags($database, $game, $settings);
        $tags_merged = array_unique($tags, SORT_REGULAR);
        tagsMerged($database, $game, $tags_merged);
    }

    echo "Job done!";
}

