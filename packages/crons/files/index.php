<?php
require "helpers/helpers.php";
require "helpers/header.php";

require 'helpers/init.php';

if (isset($_GET['do'])) {
    switch ($_GET['do']) {
        case "games":
        {
            $file = "scripts/games/";
            $title = "Games";
            break;
        }
        case "utils":
        {
            $file = "scripts/utils/";
            $title = "Utils";
            break;
        }
        default:
        {
            $file = false;
        }
    }

    try {
        if ($file) {
            if (isset($absolute)) {
                require $file;
            } else {
                require $file . (isset($_GET['help']) ? "help.php" : "index.php");
            }

            preventParallel($database, $title . "-" . $_GET['action']);
            import($database, $SesClient);
            finishParallel($database, $title . "-" . $_GET['action']);

            if (isset($_GET['log'])) {
                var_dump($database->log());
            }
        } else {
            echo 'No process selected';
        }
    } catch (Exception $e) {
        if (isset($_GET['log'])) {
            var_dump($database->log());
        }
        echo "Failed with: ", $e->getMessage(), "\n";
    }
}

require "helpers/footer.php";
