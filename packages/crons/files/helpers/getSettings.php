<?php

function findSetting($settings, $name)
{
    foreach ($settings as $setting) {
        if ($setting['name'] === $name) {
            return $setting['value'];
        }
    }

    return false;
}

function findCron($name, $crons)
{
    foreach ($crons as $cron) {
        if ($cron['id'] === $name) {
            return $cron;
        }
    }

    return false;
}

function getSettings($database, $item = null)
{
    $settings = $database->select("settings", ["name", "value"]);

    if (findSetting($settings, "crons:on") === 0) {
        throw new Exception('Crons are disabled!');
    }

    if ($item === null) {
        $crons = $database->select("crons", ["id",
                                             "last_run",
                                             "is_on",
                                             "refetch",
                                             "run_period",
                                             "is_running",
                                             "items_per_batch",
                                             "blocking"]);
    } else {
        $crons = $database->select("crons", ["id",
                                             "last_run",
                                             "is_on",
                                             "refetch",
                                             "run_period",
                                             "is_running",
                                             "items_per_batch",
                                             "blocking"], ['id' => $item]);
        $crons = $crons[0];
    }

    return ['crons' => $crons, 'settings' => $settings];
}