<?php
if (!isset($_GET['status'])) { ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta http-equiv="content-language" content="en"/>
        <meta content="initial-scale=1, maximum-scale=1 shrink-to-fit=no, user-scalable=no" name="viewport"/>
        <meta content="ie=edge" http-equiv="x-ua-compatible"/>
        <title>Explain Games - Party crons</title>
    </head>
<body>
<article>
    <a href="<?= getenv('ADMIN_HOST'); ?>/crons">Back to Import</a>

    <h2>
        <?php if (isset($_GET['do']) && isset($_GET['action'])) { ?>
        Execute: <?= $_GET['do'] . (isset($_GET['do']) ? (" - " . $_GET['action']) : "") . (isset($_GET['list']) ? (" - " . $_GET['list']) : "") ?></h2>
    <?php } ?>

    <h4>To work</h4>
    <ul>
        By default all files do dry run.
        <li>log - all/update/process - show logs (default is update)</li>
        <li>debug - execute && show logs even if feature disabled in admin</li>
        <li>help - show available settings</li>
        <li>test_upload - test image upload in dev mode, ignoring default behaviour</li>

        <?php if (isDev()) { ?>
            <div><a href="/?do=games&action=users">Users games</a></div>
            <div><a href="/?do=games&action=newgames">Fill new games</a></div>
            <div><a href="/?do=games&action=games">Update existing games</a></div>
            <div><a href="/?do=games&action=communities_games">Prepare communities base games</a></div>
            <div><a href="/?do=games&action=communities_items">Prepare communities expansion games</a></div>
            <br/>
            <div><a href="/?do=games&action=fix">FIX</a></div>
            <br/>
            <br/>
            <div><a href="<?= getenv('PMA_HOST'); ?>">PMA</a></div>

        <?php } ?>
    </ul>
</article>
<?php } ?>