<?php

require 'init.php';
require 'helpers.php';
require 'ScoreReason.php';

import($database);

function import($database)
{
    try {
        // start transaction (?)
        $database->action(function ($database) {

            throw new Exception('Just stop!!! (Not to change date in DB until all seems really working! Then please remove ME!');
        });
    } catch (Exception $e) {
        var_dump($database->log());
        echo "Failed with: ", $e->getMessage(), "\n";
    }
}
