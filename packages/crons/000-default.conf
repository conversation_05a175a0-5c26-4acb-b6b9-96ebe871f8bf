<VirtualHost *:80>
    ServerAdmin <EMAIL>
    DocumentRoot /var/www

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined

    <Directory "/var/www/html">
        # use mod_rewrite for pretty URL support
        RewriteEngine on

        # if $showScriptName is false in UrlManager, do not allow accessing URLs with script name
        RewriteRule ^index.php/ - [L,R=404]

        # If a directory or a file exists, use the request directly
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d

        # Otherwise forward the request to index.php
        RewriteRule . index.php

        # ...other settings...
    </Directory>
</VirtualHost>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
