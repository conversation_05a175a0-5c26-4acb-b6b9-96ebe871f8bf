### Setup

 * install docker

 * run `pnpm install` on `./packages/server`
 * run `pnpm install` on `./packages/api`
 * run `pnpm install` on `./packages/common`

 * Add to hosts (`/etc/hosts` on Linux and Mac) file:
```
127.0.0.1 lparty.explain.games
127.0.0.1 lparty-api.explain.games
127.0.0.1 lparty-crons.explain.games
127.0.0.1 lparty-pma.explain.games
```
 * see `config/nginx/router-local/certs/readme.md` for details on setting up dev certificates

### Run

 * run `docker-compose -f docker-compose-local.yaml up` on `./`
 * go to `/packages/db/migrations`
 * go to `lparty-pma.explain.games`
 * run all migrations

 * Now You can open page
 * go to `lparty.explain.games`